import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.jsx';
import AuthPage from './pages/AuthPage.jsx';
import './style.css';

const root = ReactDOM.createRoot(document.getElementById('app'));

function MainRouter() {
  const [route, setRoute] = React.useState(window.location.pathname);

  React.useEffect(() => {
    const onPopState = () => setRoute(window.location.pathname);
    window.addEventListener('popstate', onPopState);
    return () => window.removeEventListener('popstate', onPopState);
  }, []);

  React.useEffect(() => {
    window.routeTo = (path) => {
      window.history.pushState({}, '', path);
      setRoute(path);
    };
  }, []);

  if (route === '/auth') return <AuthPage />;
  return <App />;
}

root.render(
  <React.StrictMode>
    <MainRouter />
  </React.StrictMode>
);
