import React from 'react'
import { motion } from 'framer-motion'
import { 
  Globe, 
  FileText, 
  Search, 
  Brain, 
  Settings, 
  Lightbulb,
  Monitor,
  Code,
  FolderOpen,
  BarChart3,
  MessageSquare,
  History,
  Upload,
  List,
  Check,
  ArrowRight,
  Github,
  Twitter
} from 'lucide-react'

function App() {
  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <section className="section-padding min-h-screen flex items-center">
        <div className="max-w-7xl mx-auto">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-5xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-soft-white via-electric-blue to-lime-green bg-clip-text text-transparent">
              Tired of Paying for Tools Like Manus?
            </h1>
            <h2 className="text-3xl lg:text-5xl font-mono font-semibold mb-8 text-electric-blue">
              Meet Lemix – Your Autonomous AI Agent. Free to Start.
            </h2>
            <p className="text-xl lg:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Lemix handles complex tasks, automates workflows, and gives you full control over powerful AI – all from one sleek interface.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <motion.button 
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="glow-button text-white text-lg px-8 py-4 flex items-center gap-3"
              >
                Get Started Free
                <ArrowRight className="w-5 h-5" />
              </motion.button>
              
              <motion.button 
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="glass-card px-8 py-4 text-lg font-medium text-soft-white hover:text-electric-blue transition-colors duration-300"
              >
                See It in Action
              </motion.button>
            </div>
          </motion.div>
          
          {/* Hero Visual */}
          <motion.div 
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
            className="mt-16 relative"
          >
            <div className="glass-card p-8 max-w-4xl mx-auto">
              <div className="bg-black/50 rounded-xl p-6 font-mono">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="ml-4 text-gray-400">Lemix Terminal</span>
                </div>
                <div className="space-y-2">
                  <div className="text-lime-green">$ lemix "Analyze this website and create a summary report"</div>
                  <div className="text-gray-400">🌐 Opening browser...</div>
                  <div className="text-gray-400">📊 Extracting data...</div>
                  <div className="text-gray-400">📝 Generating report...</div>
                  <div className="text-electric-blue">✅ Task completed! Report saved to /reports/website-analysis.pdf</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-soft-white">
              Smarter Than a Chatbot. <span className="text-electric-blue">Faster Than an Assistant.</span>
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Globe,
                title: "Web Automation",
                description: "Interact with websites, extract data, fill forms, and navigate like a pro."
              },
              {
                icon: FileText,
                title: "File & Task Control",
                description: "Rename files, organize folders, generate PDFs, summarize documents."
              },
              {
                icon: Search,
                title: "Internet Research",
                description: "Search the web, compile reports, and deliver cited results in seconds."
              },
              {
                icon: Brain,
                title: "AI Agent Logic",
                description: "Lemix plans, reasons, and executes – like a real assistant."
              },
              {
                icon: Settings,
                title: "Custom Workflows",
                description: "Build or modify task flows – from basic to advanced."
              },
              {
                icon: Lightbulb,
                title: "Real-Time Planning",
                description: "Breaks down complex goals into executable steps and adapts on the fly."
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="feature-card"
              >
                <feature.icon className="w-12 h-12 text-electric-blue mb-4" />
                <h3 className="text-xl font-semibold mb-3 text-soft-white">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Built for Productivity Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-soft-white">
              Replace Your Stack With <span className="text-lime-green">One Superagent</span>
            </h2>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-6">
            {[
              { icon: Monitor, label: "Browser Tool" },
              { icon: Code, label: "Python Code Executor" },
              { icon: FolderOpen, label: "File Manager" },
              { icon: Search, label: "Web Search & Scraper" },
              { icon: List, label: "Task Planner" },
              { icon: BarChart3, label: "Summary & Report Generator" },
              { icon: Brain, label: "Multi-Model Support" }
            ].map((tool, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="glass-card p-6 text-center hover:bg-white/10 transition-all duration-300"
              >
                <tool.icon className="w-8 h-8 text-electric-blue mx-auto mb-3" />
                <p className="text-sm text-gray-300 font-medium">{tool.label}</p>
              </motion.div>
            ))}
          </div>

          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="text-center text-xl text-gray-300 mt-12"
          >
            Everything you need, in one window.
          </motion.p>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-soft-white">
              3 Steps to Let <span className="text-electric-blue">Lemix Work for You</span>
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: "1️⃣",
                title: "Login & Connect Your Model",
                description: "Use your API key (OpenAI, Claude, etc.) to power your agent."
              },
              {
                step: "2️⃣",
                title: "Start a Chat",
                description: "Describe your task in natural language."
              },
              {
                step: "3️⃣",
                title: "Watch It Execute",
                description: "Lemix plans, acts, and completes it — in real time."
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
                className="text-center"
              >
                <div className="text-6xl mb-6">{step.step}</div>
                <h3 className="text-2xl font-semibold mb-4 text-soft-white">{step.title}</h3>
                <p className="text-gray-300 text-lg">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Chat Interface Preview */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-soft-white">
              Your Private <span className="text-lime-green">Command Center</span>
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="glass-card p-8 max-w-6xl mx-auto"
          >
            <div className="bg-black/50 rounded-xl p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="ml-4 text-gray-400 font-mono">Lemix Chat Interface</span>
                </div>
                <div className="flex gap-4 text-sm text-gray-400">
                  <span>🧠 GPT-4</span>
                  <span>📁 Files: 3</span>
                  <span>⚙️ Tools: Active</span>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex gap-3">
                  <div className="w-8 h-8 bg-electric-blue rounded-full flex items-center justify-center text-white font-bold text-sm">U</div>
                  <div className="bg-gray-800 rounded-lg p-3 flex-1">
                    <p className="text-soft-white">Create a comprehensive market analysis report for the SaaS industry, including competitor pricing and feature comparison</p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="w-8 h-8 bg-lime-green rounded-full flex items-center justify-center text-black font-bold text-sm">L</div>
                  <div className="bg-gray-700 rounded-lg p-3 flex-1">
                    <p className="text-soft-white mb-2">I'll help you create a comprehensive SaaS market analysis. Let me break this down into steps:</p>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2 text-lime-green">
                        <Check className="w-4 h-4" />
                        <span>Research top SaaS companies and their pricing models</span>
                      </div>
                      <div className="flex items-center gap-2 text-electric-blue">
                        <div className="w-4 h-4 border-2 border-electric-blue rounded animate-spin"></div>
                        <span>Analyzing competitor features and positioning</span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-400">
                        <div className="w-4 h-4 border-2 border-gray-400 rounded"></div>
                        <span>Generate comprehensive report with visualizations</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex items-center gap-4">
                <div className="flex-1 bg-gray-800 rounded-lg p-3 flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-400">Type your next task...</span>
                </div>
                <button className="bg-electric-blue hover:bg-blue-600 p-3 rounded-lg transition-colors">
                  <ArrowRight className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>

            <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              {[
                { icon: MessageSquare, label: "Interactive Task Chat" },
                { icon: History, label: "Tool History Panel" },
                { icon: Upload, label: "File Upload" },
                { icon: List, label: "Plan Breakdown" }
              ].map((feature, index) => (
                <div key={index} className="flex flex-col items-center gap-2">
                  <feature.icon className="w-6 h-6 text-electric-blue" />
                  <span className="text-sm text-gray-300">{feature.label}</span>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-soft-white">
              Simple Pricing
            </h2>
            <p className="text-xl text-gray-300">Start using Lemix free – No hidden fees.</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="max-w-md mx-auto"
          >
            <div className="glass-card p-8 text-center relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-electric-blue/10 to-lime-green/10"></div>
              <div className="relative">
                <h3 className="text-2xl font-bold text-soft-white mb-2">Starter</h3>
                <div className="text-6xl font-bold text-electric-blue mb-6">$0</div>
                <ul className="space-y-4 text-left mb-8">
                  {[
                    "All features unlocked",
                    "Unlimited chat sessions",
                    "BYO API key",
                    "Access to tools",
                    "Use in browser"
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-lime-green" />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button className="glow-button w-full text-white font-semibold">
                  Get Started Free
                </button>
                <p className="text-sm text-gray-400 mt-4">🔒 No credit card required to start</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section-padding">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-soft-white">
              Frequently Asked Questions
            </h2>
          </motion.div>

          <div className="space-y-6">
            {[
              {
                question: "Is Lemix really powerful enough to replace other tools?",
                answer: "Yes. Lemix uses the latest AI models and structured planning to complete tasks across the web, files, and code."
              },
              {
                question: "Do I need to install anything?",
                answer: "No. Lemix works entirely in the browser. Just log in and start using it."
              },
              {
                question: "Can I use my own API key?",
                answer: "Yes. Bring your OpenAI or Claude API keys and start immediately."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="glass-card p-6"
              >
                <h3 className="text-xl font-semibold text-soft-white mb-3">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="section-padding">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-soft-white">
              Ready to Experience <span className="text-electric-blue">True Autonomy?</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Let Lemix handle the boring work while you focus on what matters.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="glow-button text-white text-lg px-8 py-4 flex items-center gap-3 mx-auto"
            >
              Get Started Free — Launch Your First Agent Now
              <ArrowRight className="w-5 h-5" />
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <h3 className="text-2xl font-bold text-electric-blue font-mono">Lemix</h3>
            </div>

            <div className="flex items-center gap-8 mb-6 md:mb-0">
              <a href="#" className="text-gray-400 hover:text-soft-white transition-colors">Home</a>
              <a href="#" className="text-gray-400 hover:text-soft-white transition-colors">Features</a>
              <a href="#" className="text-gray-400 hover:text-soft-white transition-colors">FAQ</a>
              <a href="#" className="text-gray-400 hover:text-soft-white transition-colors">Privacy</a>
            </div>

            <div className="flex items-center gap-4">
              <a href="#" className="text-gray-400 hover:text-electric-blue transition-colors">
                <Github className="w-6 h-6" />
              </a>
              <a href="#" className="text-gray-400 hover:text-electric-blue transition-colors">
                <Twitter className="w-6 h-6" />
              </a>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-white/10 text-center">
            <p className="text-gray-400">Copyright © 2025 Lemix. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
