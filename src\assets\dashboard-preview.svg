<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="url(#darkGradient)"/>
  
  <!-- Grid pattern -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(59, 130, 246, 0.1)" stroke-width="1"/>
    </pattern>
    <linearGradient id="darkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0B0E14;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1A1F2B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:0.6" />
    </linearGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A3E635;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#65A30D;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <rect width="400" height="300" fill="url(#grid)"/>
  
  <!-- Terminal window -->
  <rect x="20" y="20" width="120" height="80" rx="8" fill="rgba(26, 31, 43, 0.8)" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1"/>
  <rect x="25" y="25" width="10" height="6" rx="2" fill="#A3E635"/>
  <text x="40" y="35" font-family="monospace" font-size="8" fill="#A3E635">&gt;_</text>
  <line x1="25" y1="45" x2="130" y2="45" stroke="rgba(163, 230, 53, 0.3)" stroke-width="1"/>
  <line x1="25" y1="55" x2="110" y2="55" stroke="rgba(163, 230, 53, 0.3)" stroke-width="1"/>
  <line x1="25" y1="65" x2="120" y2="65" stroke="rgba(163, 230, 53, 0.3)" stroke-width="1"/>
  
  <!-- Chart area -->
  <rect x="20" y="120" width="180" height="120" rx="12" fill="rgba(26, 31, 43, 0.8)" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1"/>
  <!-- Chart line -->
  <path d="M 35 200 Q 60 180 85 190 T 135 170 T 185 160" stroke="#3B82F6" stroke-width="3" fill="none"/>
  <path d="M 35 200 Q 60 180 85 190 T 135 170 T 185 160 L 185 225 L 35 225 Z" fill="url(#blueGradient)"/>
  
  <!-- Pie chart -->
  <rect x="220" y="20" width="80" height="80" rx="8" fill="rgba(26, 31, 43, 0.8)" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1"/>
  <circle cx="260" cy="60" r="25" fill="none" stroke="rgba(59, 130, 246, 0.3)" stroke-width="2"/>
  <path d="M 260 35 A 25 25 0 0 1 285 60 L 260 60 Z" fill="#A3E635"/>
  <path d="M 285 60 A 25 25 0 1 1 245 75 L 260 60 Z" fill="#3B82F6"/>
  
  <!-- Data cards -->
  <rect x="220" y="120" width="80" height="60" rx="8" fill="rgba(26, 31, 43, 0.8)" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1"/>
  <line x1="230" y1="135" x2="290" y2="135" stroke="rgba(163, 230, 53, 0.5)" stroke-width="2"/>
  <line x1="230" y1="145" x2="280" y2="145" stroke="rgba(163, 230, 53, 0.3)" stroke-width="1"/>
  <line x1="230" y1="155" x2="285" y2="155" stroke="rgba(163, 230, 53, 0.3)" stroke-width="1"/>
  <circle cx="285" cy="165" r="3" fill="#A3E635"/>
  
  <!-- User profile card -->
  <rect x="20" y="260" width="120" height="30" rx="6" fill="rgba(26, 31, 43, 0.8)" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1"/>
  <rect x="30" y="267" width="16" height="16" rx="8" fill="none" stroke="#A3E635" stroke-width="2"/>
  <circle cx="38" cy="271" r="3" fill="none" stroke="#A3E635" stroke-width="1"/>
  <path d="M 33 278 Q 38 276 43 278" stroke="#A3E635" stroke-width="1" fill="none"/>
  
  <!-- Search icon -->
  <rect x="320" y="20" width="60" height="60" rx="8" fill="rgba(26, 31, 43, 0.8)" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1"/>
  <circle cx="345" cy="45" r="12" fill="none" stroke="#3B82F6" stroke-width="2"/>
  <line x1="354" y1="54" x2="365" y2="65" stroke="#3B82F6" stroke-width="2"/>
  
  <!-- Keypad -->
  <rect x="320" y="120" width="60" height="80" rx="8" fill="rgba(26, 31, 43, 0.8)" stroke="rgba(59, 130, 246, 0.3)" stroke-width="1"/>
  <circle cx="335" cy="135" r="4" fill="#3B82F6"/>
  <circle cx="350" cy="135" r="4" fill="#3B82F6"/>
  <circle cx="365" cy="135" r="4" fill="#3B82F6"/>
  <circle cx="335" cy="150" r="4" fill="#3B82F6"/>
  <circle cx="350" cy="150" r="4" fill="#3B82F6"/>
  <circle cx="365" cy="150" r="4" fill="#3B82F6"/>
  <circle cx="335" cy="165" r="4" fill="#3B82F6"/>
  <circle cx="350" cy="165" r="4" fill="#3B82F6"/>
  <circle cx="365" cy="165" r="4" fill="#3B82F6"/>
  
  <!-- Glow effects -->
  <circle cx="260" cy="60" r="30" fill="none" stroke="rgba(59, 130, 246, 0.1)" stroke-width="8"/>
  <circle cx="350" cy="45" r="18" fill="none" stroke="rgba(59, 130, 246, 0.1)" stroke-width="4"/>
</svg>
