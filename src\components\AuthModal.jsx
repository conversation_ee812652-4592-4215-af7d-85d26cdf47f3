import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function AuthModal({ open, onClose }) {
  const [isSignUp, setIsSignUp] = useState(false);

  return (
    <AnimatePresence>
      {open && (
        <motion.div 
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="glass-card p-8 w-full max-w-md shadow-xl relative"
          >
            <button onClick={onClose} className="absolute top-4 right-4 text-gray-400 hover:text-electric-blue text-2xl font-bold">&times;</button>
            <h2 className="text-3xl font-bold text-electric-blue mb-6 text-center">
              {isSignUp ? 'Sign Up' : 'Sign In'} to Lemix
            </h2>
            <button
              className="w-full flex items-center justify-center gap-3 bg-electric-blue hover:bg-blue-600 text-white font-semibold py-3 rounded-lg mb-6 transition-colors"
              onClick={() => alert('Google Auth coming soon!')}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" strokeWidth="0"></g><g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M21.805 10.023h-9.765v3.977h5.617c-.242 1.242-1.453 3.648-5.617 3.648-3.383 0-6.148-2.797-6.148-6.25s2.765-6.25 6.148-6.25c1.922 0 3.211.82 3.953 1.523l2.703-2.633c-1.703-1.57-3.906-2.523-6.656-2.523-5.523 0-10 4.477-10 10s4.477 10 10 10c5.742 0 9.547-4.023 9.547-9.695 0-.652-.07-1.148-.148-1.547z" fill="#fff"/><path d="M3.545 7.545l3.281 2.406c.891-1.781 2.578-2.906 4.414-2.906 1.242 0 2.414.484 3.289 1.367l2.484-2.484c-1.484-1.391-3.414-2.227-5.773-2.227-3.672 0-6.703 2.992-6.703 6.75 0 1.07.211 2.086.586 3.008l3.422-2.414c-.07-.227-.117-.469-.117-.719 0-1.242 1.008-2.25 2.25-2.25z" fill="#fff"/></g></svg>
              Continue with Google
            </button>
            <form className="space-y-4">
              {isSignUp && (
                <input
                  type="text"
                  placeholder="Full Name"
                  className="w-full px-4 py-3 rounded-lg bg-dark-secondary text-white border border-white/10 focus:outline-none focus:border-electric-blue"
                  required
                />
              )}
              <input
                type="email"
                placeholder="Email"
                className="w-full px-4 py-3 rounded-lg bg-dark-secondary text-white border border-white/10 focus:outline-none focus:border-electric-blue"
                required
              />
              <input
                type="password"
                placeholder="Password"
                className="w-full px-4 py-3 rounded-lg bg-dark-secondary text-white border border-white/10 focus:outline-none focus:border-electric-blue"
                required
              />
              <motion.button
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                type="submit"
                className="w-full glow-button text-white font-semibold py-3 rounded-lg mt-2"
              >
                {isSignUp ? 'Sign Up' : 'Sign In'}
              </motion.button>
            </form>
            <div className="text-center mt-6">
              <button
                className="text-electric-blue hover:underline text-sm"
                onClick={() => setIsSignUp(!isSignUp)}
              >
                {isSignUp ? 'Already have an account? Sign In' : "Don't have an account? Sign Up"}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
