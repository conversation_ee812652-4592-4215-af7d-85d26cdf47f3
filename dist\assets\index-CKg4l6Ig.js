(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))o(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const h of d.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&o(h)}).observe(document,{childList:!0,subtree:!0});function u(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function o(c){if(c.ep)return;c.ep=!0;const d=u(c);fetch(c.href,d)}})();function ep(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var pr={exports:{}},il={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Om;function n1(){if(Om)return il;Om=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(o,c,d){var h=null;if(d!==void 0&&(h=""+d),c.key!==void 0&&(h=""+c.key),"key"in c){d={};for(var m in c)m!=="key"&&(d[m]=c[m])}else d=c;return c=d.ref,{$$typeof:i,type:o,key:h,ref:c!==void 0?c:null,props:d}}return il.Fragment=l,il.jsx=u,il.jsxs=u,il}var Nm;function a1(){return Nm||(Nm=1,pr.exports=n1()),pr.exports}var R=a1(),gr={exports:{}},at={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vm;function i1(){if(Vm)return at;Vm=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),h=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),x=Symbol.iterator;function M(S){return S===null||typeof S!="object"?null:(S=x&&S[x]||S["@@iterator"],typeof S=="function"?S:null)}var V={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},B=Object.assign,L={};function Y(S,w,Z){this.props=S,this.context=w,this.refs=L,this.updater=Z||V}Y.prototype.isReactComponent={},Y.prototype.setState=function(S,w){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,w,"setState")},Y.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function G(){}G.prototype=Y.prototype;function J(S,w,Z){this.props=S,this.context=w,this.refs=L,this.updater=Z||V}var H=J.prototype=new G;H.constructor=J,B(H,Y.prototype),H.isPureReactComponent=!0;var it=Array.isArray,q={H:null,A:null,T:null,S:null,V:null},lt=Object.prototype.hasOwnProperty;function ft(S,w,Z,X,P,ht){return Z=ht.ref,{$$typeof:i,type:S,key:w,ref:Z!==void 0?Z:null,props:ht}}function W(S,w){return ft(S.type,w,void 0,void 0,void 0,S.props)}function At(S){return typeof S=="object"&&S!==null&&S.$$typeof===i}function Lt(S){var w={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(Z){return w[Z]})}var It=/\/+/g;function Ht(S,w){return typeof S=="object"&&S!==null&&S.key!=null?Lt(""+S.key):w.toString(36)}function Je(){}function Le(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(Je,Je):(S.status="pending",S.then(function(w){S.status==="pending"&&(S.status="fulfilled",S.value=w)},function(w){S.status==="pending"&&(S.status="rejected",S.reason=w)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Jt(S,w,Z,X,P){var ht=typeof S;(ht==="undefined"||ht==="boolean")&&(S=null);var nt=!1;if(S===null)nt=!0;else switch(ht){case"bigint":case"string":case"number":nt=!0;break;case"object":switch(S.$$typeof){case i:case l:nt=!0;break;case v:return nt=S._init,Jt(nt(S._payload),w,Z,X,P)}}if(nt)return P=P(S),nt=X===""?"."+Ht(S,0):X,it(P)?(Z="",nt!=null&&(Z=nt.replace(It,"$&/")+"/"),Jt(P,w,Z,"",function(dn){return dn})):P!=null&&(At(P)&&(P=W(P,Z+(P.key==null||S&&S.key===P.key?"":(""+P.key).replace(It,"$&/")+"/")+nt)),w.push(P)),1;nt=0;var he=X===""?".":X+":";if(it(S))for(var Mt=0;Mt<S.length;Mt++)X=S[Mt],ht=he+Ht(X,Mt),nt+=Jt(X,w,Z,ht,P);else if(Mt=M(S),typeof Mt=="function")for(S=Mt.call(S),Mt=0;!(X=S.next()).done;)X=X.value,ht=he+Ht(X,Mt++),nt+=Jt(X,w,Z,ht,P);else if(ht==="object"){if(typeof S.then=="function")return Jt(Le(S),w,Z,X,P);throw w=String(S),Error("Objects are not valid as a React child (found: "+(w==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":w)+"). If you meant to render a collection of children, use an array instead.")}return nt}function j(S,w,Z){if(S==null)return S;var X=[],P=0;return Jt(S,X,"","",function(ht){return w.call(Z,ht,P++)}),X}function U(S){if(S._status===-1){var w=S._result;w=w(),w.then(function(Z){(S._status===0||S._status===-1)&&(S._status=1,S._result=Z)},function(Z){(S._status===0||S._status===-1)&&(S._status=2,S._result=Z)}),S._status===-1&&(S._status=0,S._result=w)}if(S._status===1)return S._result.default;throw S._result}var K=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var w=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(w))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function mt(){}return at.Children={map:j,forEach:function(S,w,Z){j(S,function(){w.apply(this,arguments)},Z)},count:function(S){var w=0;return j(S,function(){w++}),w},toArray:function(S){return j(S,function(w){return w})||[]},only:function(S){if(!At(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},at.Component=Y,at.Fragment=u,at.Profiler=c,at.PureComponent=J,at.StrictMode=o,at.Suspense=g,at.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=q,at.__COMPILER_RUNTIME={__proto__:null,c:function(S){return q.H.useMemoCache(S)}},at.cache=function(S){return function(){return S.apply(null,arguments)}},at.cloneElement=function(S,w,Z){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var X=B({},S.props),P=S.key,ht=void 0;if(w!=null)for(nt in w.ref!==void 0&&(ht=void 0),w.key!==void 0&&(P=""+w.key),w)!lt.call(w,nt)||nt==="key"||nt==="__self"||nt==="__source"||nt==="ref"&&w.ref===void 0||(X[nt]=w[nt]);var nt=arguments.length-2;if(nt===1)X.children=Z;else if(1<nt){for(var he=Array(nt),Mt=0;Mt<nt;Mt++)he[Mt]=arguments[Mt+2];X.children=he}return ft(S.type,P,void 0,void 0,ht,X)},at.createContext=function(S){return S={$$typeof:h,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:d,_context:S},S},at.createElement=function(S,w,Z){var X,P={},ht=null;if(w!=null)for(X in w.key!==void 0&&(ht=""+w.key),w)lt.call(w,X)&&X!=="key"&&X!=="__self"&&X!=="__source"&&(P[X]=w[X]);var nt=arguments.length-2;if(nt===1)P.children=Z;else if(1<nt){for(var he=Array(nt),Mt=0;Mt<nt;Mt++)he[Mt]=arguments[Mt+2];P.children=he}if(S&&S.defaultProps)for(X in nt=S.defaultProps,nt)P[X]===void 0&&(P[X]=nt[X]);return ft(S,ht,void 0,void 0,null,P)},at.createRef=function(){return{current:null}},at.forwardRef=function(S){return{$$typeof:m,render:S}},at.isValidElement=At,at.lazy=function(S){return{$$typeof:v,_payload:{_status:-1,_result:S},_init:U}},at.memo=function(S,w){return{$$typeof:y,type:S,compare:w===void 0?null:w}},at.startTransition=function(S){var w=q.T,Z={};q.T=Z;try{var X=S(),P=q.S;P!==null&&P(Z,X),typeof X=="object"&&X!==null&&typeof X.then=="function"&&X.then(mt,K)}catch(ht){K(ht)}finally{q.T=w}},at.unstable_useCacheRefresh=function(){return q.H.useCacheRefresh()},at.use=function(S){return q.H.use(S)},at.useActionState=function(S,w,Z){return q.H.useActionState(S,w,Z)},at.useCallback=function(S,w){return q.H.useCallback(S,w)},at.useContext=function(S){return q.H.useContext(S)},at.useDebugValue=function(){},at.useDeferredValue=function(S,w){return q.H.useDeferredValue(S,w)},at.useEffect=function(S,w,Z){var X=q.H;if(typeof Z=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return X.useEffect(S,w)},at.useId=function(){return q.H.useId()},at.useImperativeHandle=function(S,w,Z){return q.H.useImperativeHandle(S,w,Z)},at.useInsertionEffect=function(S,w){return q.H.useInsertionEffect(S,w)},at.useLayoutEffect=function(S,w){return q.H.useLayoutEffect(S,w)},at.useMemo=function(S,w){return q.H.useMemo(S,w)},at.useOptimistic=function(S,w){return q.H.useOptimistic(S,w)},at.useReducer=function(S,w,Z){return q.H.useReducer(S,w,Z)},at.useRef=function(S){return q.H.useRef(S)},at.useState=function(S){return q.H.useState(S)},at.useSyncExternalStore=function(S,w,Z){return q.H.useSyncExternalStore(S,w,Z)},at.useTransition=function(){return q.H.useTransition()},at.version="19.1.0",at}var Cm;function nc(){return Cm||(Cm=1,gr.exports=i1()),gr.exports}var et=nc();const l1=ep(et);var vr={exports:{}},ll={},br={exports:{}},xr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jm;function s1(){return jm||(jm=1,function(i){function l(j,U){var K=j.length;j.push(U);t:for(;0<K;){var mt=K-1>>>1,S=j[mt];if(0<c(S,U))j[mt]=U,j[K]=S,K=mt;else break t}}function u(j){return j.length===0?null:j[0]}function o(j){if(j.length===0)return null;var U=j[0],K=j.pop();if(K!==U){j[0]=K;t:for(var mt=0,S=j.length,w=S>>>1;mt<w;){var Z=2*(mt+1)-1,X=j[Z],P=Z+1,ht=j[P];if(0>c(X,K))P<S&&0>c(ht,X)?(j[mt]=ht,j[P]=K,mt=P):(j[mt]=X,j[Z]=K,mt=Z);else if(P<S&&0>c(ht,K))j[mt]=ht,j[P]=K,mt=P;else break t}}return U}function c(j,U){var K=j.sortIndex-U.sortIndex;return K!==0?K:j.id-U.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;i.unstable_now=function(){return d.now()}}else{var h=Date,m=h.now();i.unstable_now=function(){return h.now()-m}}var g=[],y=[],v=1,x=null,M=3,V=!1,B=!1,L=!1,Y=!1,G=typeof setTimeout=="function"?setTimeout:null,J=typeof clearTimeout=="function"?clearTimeout:null,H=typeof setImmediate<"u"?setImmediate:null;function it(j){for(var U=u(y);U!==null;){if(U.callback===null)o(y);else if(U.startTime<=j)o(y),U.sortIndex=U.expirationTime,l(g,U);else break;U=u(y)}}function q(j){if(L=!1,it(j),!B)if(u(g)!==null)B=!0,lt||(lt=!0,Ht());else{var U=u(y);U!==null&&Jt(q,U.startTime-j)}}var lt=!1,ft=-1,W=5,At=-1;function Lt(){return Y?!0:!(i.unstable_now()-At<W)}function It(){if(Y=!1,lt){var j=i.unstable_now();At=j;var U=!0;try{t:{B=!1,L&&(L=!1,J(ft),ft=-1),V=!0;var K=M;try{e:{for(it(j),x=u(g);x!==null&&!(x.expirationTime>j&&Lt());){var mt=x.callback;if(typeof mt=="function"){x.callback=null,M=x.priorityLevel;var S=mt(x.expirationTime<=j);if(j=i.unstable_now(),typeof S=="function"){x.callback=S,it(j),U=!0;break e}x===u(g)&&o(g),it(j)}else o(g);x=u(g)}if(x!==null)U=!0;else{var w=u(y);w!==null&&Jt(q,w.startTime-j),U=!1}}break t}finally{x=null,M=K,V=!1}U=void 0}}finally{U?Ht():lt=!1}}}var Ht;if(typeof H=="function")Ht=function(){H(It)};else if(typeof MessageChannel<"u"){var Je=new MessageChannel,Le=Je.port2;Je.port1.onmessage=It,Ht=function(){Le.postMessage(null)}}else Ht=function(){G(It,0)};function Jt(j,U){ft=G(function(){j(i.unstable_now())},U)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(j){j.callback=null},i.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):W=0<j?Math.floor(1e3/j):5},i.unstable_getCurrentPriorityLevel=function(){return M},i.unstable_next=function(j){switch(M){case 1:case 2:case 3:var U=3;break;default:U=M}var K=M;M=U;try{return j()}finally{M=K}},i.unstable_requestPaint=function(){Y=!0},i.unstable_runWithPriority=function(j,U){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var K=M;M=j;try{return U()}finally{M=K}},i.unstable_scheduleCallback=function(j,U,K){var mt=i.unstable_now();switch(typeof K=="object"&&K!==null?(K=K.delay,K=typeof K=="number"&&0<K?mt+K:mt):K=mt,j){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=K+S,j={id:v++,callback:U,priorityLevel:j,startTime:K,expirationTime:S,sortIndex:-1},K>mt?(j.sortIndex=K,l(y,j),u(g)===null&&j===u(y)&&(L?(J(ft),ft=-1):L=!0,Jt(q,K-mt))):(j.sortIndex=S,l(g,j),B||V||(B=!0,lt||(lt=!0,Ht()))),j},i.unstable_shouldYield=Lt,i.unstable_wrapCallback=function(j){var U=M;return function(){var K=M;M=U;try{return j.apply(this,arguments)}finally{M=K}}}}(xr)),xr}var zm;function u1(){return zm||(zm=1,br.exports=s1()),br.exports}var Sr={exports:{}},ne={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _m;function o1(){if(_m)return ne;_m=1;var i=nc();function l(g){var y="https://react.dev/errors/"+g;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)y+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+g+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(g,y,v){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:x==null?null:""+x,children:g,containerInfo:y,implementation:v}}var h=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function m(g,y){if(g==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return ne.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,ne.createPortal=function(g,y){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(l(299));return d(g,y,null,v)},ne.flushSync=function(g){var y=h.T,v=o.p;try{if(h.T=null,o.p=2,g)return g()}finally{h.T=y,o.p=v,o.d.f()}},ne.preconnect=function(g,y){typeof g=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,o.d.C(g,y))},ne.prefetchDNS=function(g){typeof g=="string"&&o.d.D(g)},ne.preinit=function(g,y){if(typeof g=="string"&&y&&typeof y.as=="string"){var v=y.as,x=m(v,y.crossOrigin),M=typeof y.integrity=="string"?y.integrity:void 0,V=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;v==="style"?o.d.S(g,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:x,integrity:M,fetchPriority:V}):v==="script"&&o.d.X(g,{crossOrigin:x,integrity:M,fetchPriority:V,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},ne.preinitModule=function(g,y){if(typeof g=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var v=m(y.as,y.crossOrigin);o.d.M(g,{crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&o.d.M(g)},ne.preload=function(g,y){if(typeof g=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var v=y.as,x=m(v,y.crossOrigin);o.d.L(g,v,{crossOrigin:x,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},ne.preloadModule=function(g,y){if(typeof g=="string")if(y){var v=m(y.as,y.crossOrigin);o.d.m(g,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else o.d.m(g)},ne.requestFormReset=function(g){o.d.r(g)},ne.unstable_batchedUpdates=function(g,y){return g(y)},ne.useFormState=function(g,y,v){return h.H.useFormState(g,y,v)},ne.useFormStatus=function(){return h.H.useHostTransitionStatus()},ne.version="19.1.0",ne}var wm;function r1(){if(wm)return Sr.exports;wm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),Sr.exports=o1(),Sr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Um;function c1(){if(Um)return ll;Um=1;var i=u1(),l=nc(),u=r1();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function h(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function m(t){if(d(t)!==t)throw Error(o(188))}function g(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(o(188));return e!==t?null:t}for(var n=t,a=e;;){var s=n.return;if(s===null)break;var r=s.alternate;if(r===null){if(a=s.return,a!==null){n=a;continue}break}if(s.child===r.child){for(r=s.child;r;){if(r===n)return m(s),t;if(r===a)return m(s),e;r=r.sibling}throw Error(o(188))}if(n.return!==a.return)n=s,a=r;else{for(var f=!1,p=s.child;p;){if(p===n){f=!0,n=s,a=r;break}if(p===a){f=!0,a=s,n=r;break}p=p.sibling}if(!f){for(p=r.child;p;){if(p===n){f=!0,n=r,a=s;break}if(p===a){f=!0,a=r,n=s;break}p=p.sibling}if(!f)throw Error(o(189))}}if(n.alternate!==a)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?t:e}function y(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=y(t),e!==null)return e;t=t.sibling}return null}var v=Object.assign,x=Symbol.for("react.element"),M=Symbol.for("react.transitional.element"),V=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),Y=Symbol.for("react.profiler"),G=Symbol.for("react.provider"),J=Symbol.for("react.consumer"),H=Symbol.for("react.context"),it=Symbol.for("react.forward_ref"),q=Symbol.for("react.suspense"),lt=Symbol.for("react.suspense_list"),ft=Symbol.for("react.memo"),W=Symbol.for("react.lazy"),At=Symbol.for("react.activity"),Lt=Symbol.for("react.memo_cache_sentinel"),It=Symbol.iterator;function Ht(t){return t===null||typeof t!="object"?null:(t=It&&t[It]||t["@@iterator"],typeof t=="function"?t:null)}var Je=Symbol.for("react.client.reference");function Le(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Je?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case B:return"Fragment";case Y:return"Profiler";case L:return"StrictMode";case q:return"Suspense";case lt:return"SuspenseList";case At:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case V:return"Portal";case H:return(t.displayName||"Context")+".Provider";case J:return(t._context.displayName||"Context")+".Consumer";case it:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ft:return e=t.displayName||null,e!==null?e:Le(t.type)||"Memo";case W:e=t._payload,t=t._init;try{return Le(t(e))}catch{}}return null}var Jt=Array.isArray,j=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K={pending:!1,data:null,method:null,action:null},mt=[],S=-1;function w(t){return{current:t}}function Z(t){0>S||(t.current=mt[S],mt[S]=null,S--)}function X(t,e){S++,mt[S]=t.current,t.current=e}var P=w(null),ht=w(null),nt=w(null),he=w(null);function Mt(t,e){switch(X(nt,e),X(ht,t),X(P,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?nm(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=nm(e),t=am(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Z(P),X(P,t)}function dn(){Z(P),Z(ht),Z(nt)}function tu(t){t.memoizedState!==null&&X(he,t);var e=P.current,n=am(e,t.type);e!==n&&(X(ht,t),X(P,n))}function Ml(t){ht.current===t&&(Z(P),Z(ht)),he.current===t&&(Z(he),Ii._currentValue=K)}var eu=Object.prototype.hasOwnProperty,nu=i.unstable_scheduleCallback,au=i.unstable_cancelCallback,_0=i.unstable_shouldYield,w0=i.unstable_requestPaint,He=i.unstable_now,U0=i.unstable_getCurrentPriorityLevel,wc=i.unstable_ImmediatePriority,Uc=i.unstable_UserBlockingPriority,El=i.unstable_NormalPriority,B0=i.unstable_LowPriority,Bc=i.unstable_IdlePriority,L0=i.log,H0=i.unstable_setDisableYieldValue,ui=null,de=null;function mn(t){if(typeof L0=="function"&&H0(t),de&&typeof de.setStrictMode=="function")try{de.setStrictMode(ui,t)}catch{}}var me=Math.clz32?Math.clz32:G0,q0=Math.log,Y0=Math.LN2;function G0(t){return t>>>=0,t===0?32:31-(q0(t)/Y0|0)|0}var Dl=256,Rl=4194304;function Gn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ol(t,e,n){var a=t.pendingLanes;if(a===0)return 0;var s=0,r=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var p=a&134217727;return p!==0?(a=p&~r,a!==0?s=Gn(a):(f&=p,f!==0?s=Gn(f):n||(n=p&~t,n!==0&&(s=Gn(n))))):(p=a&~r,p!==0?s=Gn(p):f!==0?s=Gn(f):n||(n=a&~t,n!==0&&(s=Gn(n)))),s===0?0:e!==0&&e!==s&&(e&r)===0&&(r=s&-s,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:s}function oi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function X0(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Lc(){var t=Dl;return Dl<<=1,(Dl&4194048)===0&&(Dl=256),t}function Hc(){var t=Rl;return Rl<<=1,(Rl&62914560)===0&&(Rl=4194304),t}function iu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ri(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Z0(t,e,n,a,s,r){var f=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var p=t.entanglements,b=t.expirationTimes,D=t.hiddenUpdates;for(n=f&~n;0<n;){var C=31-me(n),_=1<<C;p[C]=0,b[C]=-1;var O=D[C];if(O!==null)for(D[C]=null,C=0;C<O.length;C++){var N=O[C];N!==null&&(N.lane&=-536870913)}n&=~_}a!==0&&qc(t,a,0),r!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=r&~(f&~e))}function qc(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-me(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|n&4194090}function Yc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var a=31-me(n),s=1<<a;s&e|t[a]&e&&(t[a]|=e),n&=~s}}function lu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function su(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Gc(){var t=U.p;return t!==0?t:(t=window.event,t===void 0?32:Tm(t.type))}function Q0(t,e){var n=U.p;try{return U.p=t,e()}finally{U.p=n}}var yn=Math.random().toString(36).slice(2),te="__reactFiber$"+yn,se="__reactProps$"+yn,ha="__reactContainer$"+yn,uu="__reactEvents$"+yn,K0="__reactListeners$"+yn,k0="__reactHandles$"+yn,Xc="__reactResources$"+yn,ci="__reactMarker$"+yn;function ou(t){delete t[te],delete t[se],delete t[uu],delete t[K0],delete t[k0]}function da(t){var e=t[te];if(e)return e;for(var n=t.parentNode;n;){if(e=n[ha]||n[te]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=um(t);t!==null;){if(n=t[te])return n;t=um(t)}return e}t=n,n=t.parentNode}return null}function ma(t){if(t=t[te]||t[ha]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function fi(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function ya(t){var e=t[Xc];return e||(e=t[Xc]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Xt(t){t[ci]=!0}var Zc=new Set,Qc={};function Xn(t,e){pa(t,e),pa(t+"Capture",e)}function pa(t,e){for(Qc[t]=e,t=0;t<e.length;t++)Zc.add(e[t])}var J0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Kc={},kc={};function P0(t){return eu.call(kc,t)?!0:eu.call(Kc,t)?!1:J0.test(t)?kc[t]=!0:(Kc[t]=!0,!1)}function Nl(t,e,n){if(P0(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Vl(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Pe(t,e,n,a){if(a===null)t.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+a)}}var ru,Jc;function ga(t){if(ru===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);ru=e&&e[1]||"",Jc=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ru+t+Jc}var cu=!1;function fu(t,e){if(!t||cu)return"";cu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var _=function(){throw Error()};if(Object.defineProperty(_.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(_,[])}catch(N){var O=N}Reflect.construct(t,[],_)}else{try{_.call()}catch(N){O=N}t.call(_.prototype)}}else{try{throw Error()}catch(N){O=N}(_=t())&&typeof _.catch=="function"&&_.catch(function(){})}}catch(N){if(N&&O&&typeof N.stack=="string")return[N.stack,O.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=a.DetermineComponentFrameRoot(),f=r[0],p=r[1];if(f&&p){var b=f.split(`
`),D=p.split(`
`);for(s=a=0;a<b.length&&!b[a].includes("DetermineComponentFrameRoot");)a++;for(;s<D.length&&!D[s].includes("DetermineComponentFrameRoot");)s++;if(a===b.length||s===D.length)for(a=b.length-1,s=D.length-1;1<=a&&0<=s&&b[a]!==D[s];)s--;for(;1<=a&&0<=s;a--,s--)if(b[a]!==D[s]){if(a!==1||s!==1)do if(a--,s--,0>s||b[a]!==D[s]){var C=`
`+b[a].replace(" at new "," at ");return t.displayName&&C.includes("<anonymous>")&&(C=C.replace("<anonymous>",t.displayName)),C}while(1<=a&&0<=s);break}}}finally{cu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?ga(n):""}function F0(t){switch(t.tag){case 26:case 27:case 5:return ga(t.type);case 16:return ga("Lazy");case 13:return ga("Suspense");case 19:return ga("SuspenseList");case 0:case 15:return fu(t.type,!1);case 11:return fu(t.type.render,!1);case 1:return fu(t.type,!0);case 31:return ga("Activity");default:return""}}function Pc(t){try{var e="";do e+=F0(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Te(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Fc(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function W0(t){var e=Fc(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(f){a=""+f,r.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(f){a=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Cl(t){t._valueTracker||(t._valueTracker=W0(t))}function Wc(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),a="";return t&&(a=Fc(t)?t.checked?"true":"false":t.value),t=a,t!==n?(e.setValue(t),!0):!1}function jl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var $0=/[\n"\\]/g;function Ae(t){return t.replace($0,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function hu(t,e,n,a,s,r,f,p){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Te(e)):t.value!==""+Te(e)&&(t.value=""+Te(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?du(t,f,Te(e)):n!=null?du(t,f,Te(n)):a!=null&&t.removeAttribute("value"),s==null&&r!=null&&(t.defaultChecked=!!r),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?t.name=""+Te(p):t.removeAttribute("name")}function $c(t,e,n,a,s,r,f,p){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+Te(n):"",e=e!=null?""+Te(e):n,p||e===t.value||(t.value=e),t.defaultValue=e}a=a??s,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=p?t.checked:!!a,t.defaultChecked=!!a,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function du(t,e,n){e==="number"&&jl(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function va(t,e,n,a){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&a&&(t[n].defaultSelected=!0)}else{for(n=""+Te(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,a&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function Ic(t,e,n){if(e!=null&&(e=""+Te(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Te(n):""}function tf(t,e,n,a){if(e==null){if(a!=null){if(n!=null)throw Error(o(92));if(Jt(a)){if(1<a.length)throw Error(o(93));a=a[0]}n=a}n==null&&(n=""),e=n}n=Te(e),t.defaultValue=n,a=t.textContent,a===n&&a!==""&&a!==null&&(t.value=a)}function ba(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var I0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ef(t,e,n){var a=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,n):typeof n!="number"||n===0||I0.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function nf(t,e,n){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var s in e)a=e[s],e.hasOwnProperty(s)&&n[s]!==a&&ef(t,s,a)}else for(var r in e)e.hasOwnProperty(r)&&ef(t,r,e[r])}function mu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var tg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),eg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function zl(t){return eg.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var yu=null;function pu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var xa=null,Sa=null;function af(t){var e=ma(t);if(e&&(t=e.stateNode)){var n=t[se]||null;t:switch(t=e.stateNode,e.type){case"input":if(hu(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ae(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var a=n[e];if(a!==t&&a.form===t.form){var s=a[se]||null;if(!s)throw Error(o(90));hu(a,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<n.length;e++)a=n[e],a.form===t.form&&Wc(a)}break t;case"textarea":Ic(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&va(t,!!n.multiple,e,!1)}}}var gu=!1;function lf(t,e,n){if(gu)return t(e,n);gu=!0;try{var a=t(e);return a}finally{if(gu=!1,(xa!==null||Sa!==null)&&(vs(),xa&&(e=xa,t=Sa,Sa=xa=null,af(e),t)))for(e=0;e<t.length;e++)af(t[e])}}function hi(t,e){var n=t.stateNode;if(n===null)return null;var a=n[se]||null;if(a===null)return null;n=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(o(231,e,typeof n));return n}var Fe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vu=!1;if(Fe)try{var di={};Object.defineProperty(di,"passive",{get:function(){vu=!0}}),window.addEventListener("test",di,di),window.removeEventListener("test",di,di)}catch{vu=!1}var pn=null,bu=null,_l=null;function sf(){if(_l)return _l;var t,e=bu,n=e.length,a,s="value"in pn?pn.value:pn.textContent,r=s.length;for(t=0;t<n&&e[t]===s[t];t++);var f=n-t;for(a=1;a<=f&&e[n-a]===s[r-a];a++);return _l=s.slice(t,1<a?1-a:void 0)}function wl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ul(){return!0}function uf(){return!1}function ue(t){function e(n,a,s,r,f){this._reactName=n,this._targetInst=s,this.type=a,this.nativeEvent=r,this.target=f,this.currentTarget=null;for(var p in t)t.hasOwnProperty(p)&&(n=t[p],this[p]=n?n(r):r[p]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ul:uf,this.isPropagationStopped=uf,this}return v(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ul)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ul)},persist:function(){},isPersistent:Ul}),e}var Zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bl=ue(Zn),mi=v({},Zn,{view:0,detail:0}),ng=ue(mi),xu,Su,yi,Ll=v({},mi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Au,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==yi&&(yi&&t.type==="mousemove"?(xu=t.screenX-yi.screenX,Su=t.screenY-yi.screenY):Su=xu=0,yi=t),xu)},movementY:function(t){return"movementY"in t?t.movementY:Su}}),of=ue(Ll),ag=v({},Ll,{dataTransfer:0}),ig=ue(ag),lg=v({},mi,{relatedTarget:0}),Tu=ue(lg),sg=v({},Zn,{animationName:0,elapsedTime:0,pseudoElement:0}),ug=ue(sg),og=v({},Zn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),rg=ue(og),cg=v({},Zn,{data:0}),rf=ue(cg),fg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mg(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=dg[t])?!!e[t]:!1}function Au(){return mg}var yg=v({},mi,{key:function(t){if(t.key){var e=fg[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=wl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?hg[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Au,charCode:function(t){return t.type==="keypress"?wl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?wl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),pg=ue(yg),gg=v({},Ll,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),cf=ue(gg),vg=v({},mi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Au}),bg=ue(vg),xg=v({},Zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Sg=ue(xg),Tg=v({},Ll,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Ag=ue(Tg),Mg=v({},Zn,{newState:0,oldState:0}),Eg=ue(Mg),Dg=[9,13,27,32],Mu=Fe&&"CompositionEvent"in window,pi=null;Fe&&"documentMode"in document&&(pi=document.documentMode);var Rg=Fe&&"TextEvent"in window&&!pi,ff=Fe&&(!Mu||pi&&8<pi&&11>=pi),hf=" ",df=!1;function mf(t,e){switch(t){case"keyup":return Dg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function yf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ta=!1;function Og(t,e){switch(t){case"compositionend":return yf(e);case"keypress":return e.which!==32?null:(df=!0,hf);case"textInput":return t=e.data,t===hf&&df?null:t;default:return null}}function Ng(t,e){if(Ta)return t==="compositionend"||!Mu&&mf(t,e)?(t=sf(),_l=bu=pn=null,Ta=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return ff&&e.locale!=="ko"?null:e.data;default:return null}}var Vg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function pf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Vg[t.type]:e==="textarea"}function gf(t,e,n,a){xa?Sa?Sa.push(a):Sa=[a]:xa=a,e=Ms(e,"onChange"),0<e.length&&(n=new Bl("onChange","change",null,n,a),t.push({event:n,listeners:e}))}var gi=null,vi=null;function Cg(t){Wd(t,0)}function Hl(t){var e=fi(t);if(Wc(e))return t}function vf(t,e){if(t==="change")return e}var bf=!1;if(Fe){var Eu;if(Fe){var Du="oninput"in document;if(!Du){var xf=document.createElement("div");xf.setAttribute("oninput","return;"),Du=typeof xf.oninput=="function"}Eu=Du}else Eu=!1;bf=Eu&&(!document.documentMode||9<document.documentMode)}function Sf(){gi&&(gi.detachEvent("onpropertychange",Tf),vi=gi=null)}function Tf(t){if(t.propertyName==="value"&&Hl(vi)){var e=[];gf(e,vi,t,pu(t)),lf(Cg,e)}}function jg(t,e,n){t==="focusin"?(Sf(),gi=e,vi=n,gi.attachEvent("onpropertychange",Tf)):t==="focusout"&&Sf()}function zg(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Hl(vi)}function _g(t,e){if(t==="click")return Hl(e)}function wg(t,e){if(t==="input"||t==="change")return Hl(e)}function Ug(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ye=typeof Object.is=="function"?Object.is:Ug;function bi(t,e){if(ye(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),a=Object.keys(e);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var s=n[a];if(!eu.call(e,s)||!ye(t[s],e[s]))return!1}return!0}function Af(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Mf(t,e){var n=Af(t);t=0;for(var a;n;){if(n.nodeType===3){if(a=t+n.textContent.length,t<=e&&a>=e)return{node:n,offset:e-t};t=a}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Af(n)}}function Ef(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Ef(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Df(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=jl(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=jl(t.document)}return e}function Ru(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Bg=Fe&&"documentMode"in document&&11>=document.documentMode,Aa=null,Ou=null,xi=null,Nu=!1;function Rf(t,e,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Nu||Aa==null||Aa!==jl(a)||(a=Aa,"selectionStart"in a&&Ru(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),xi&&bi(xi,a)||(xi=a,a=Ms(Ou,"onSelect"),0<a.length&&(e=new Bl("onSelect","select",null,e,n),t.push({event:e,listeners:a}),e.target=Aa)))}function Qn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ma={animationend:Qn("Animation","AnimationEnd"),animationiteration:Qn("Animation","AnimationIteration"),animationstart:Qn("Animation","AnimationStart"),transitionrun:Qn("Transition","TransitionRun"),transitionstart:Qn("Transition","TransitionStart"),transitioncancel:Qn("Transition","TransitionCancel"),transitionend:Qn("Transition","TransitionEnd")},Vu={},Of={};Fe&&(Of=document.createElement("div").style,"AnimationEvent"in window||(delete Ma.animationend.animation,delete Ma.animationiteration.animation,delete Ma.animationstart.animation),"TransitionEvent"in window||delete Ma.transitionend.transition);function Kn(t){if(Vu[t])return Vu[t];if(!Ma[t])return t;var e=Ma[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Of)return Vu[t]=e[n];return t}var Nf=Kn("animationend"),Vf=Kn("animationiteration"),Cf=Kn("animationstart"),Lg=Kn("transitionrun"),Hg=Kn("transitionstart"),qg=Kn("transitioncancel"),jf=Kn("transitionend"),zf=new Map,Cu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Cu.push("scrollEnd");function _e(t,e){zf.set(t,e),Xn(e,[t])}var _f=new WeakMap;function Me(t,e){if(typeof t=="object"&&t!==null){var n=_f.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Pc(e)},_f.set(t,e),e)}return{value:t,source:e,stack:Pc(e)}}var Ee=[],Ea=0,ju=0;function ql(){for(var t=Ea,e=ju=Ea=0;e<t;){var n=Ee[e];Ee[e++]=null;var a=Ee[e];Ee[e++]=null;var s=Ee[e];Ee[e++]=null;var r=Ee[e];if(Ee[e++]=null,a!==null&&s!==null){var f=a.pending;f===null?s.next=s:(s.next=f.next,f.next=s),a.pending=s}r!==0&&wf(n,s,r)}}function Yl(t,e,n,a){Ee[Ea++]=t,Ee[Ea++]=e,Ee[Ea++]=n,Ee[Ea++]=a,ju|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function zu(t,e,n,a){return Yl(t,e,n,a),Gl(t)}function Da(t,e){return Yl(t,null,null,e),Gl(t)}function wf(t,e,n){t.lanes|=n;var a=t.alternate;a!==null&&(a.lanes|=n);for(var s=!1,r=t.return;r!==null;)r.childLanes|=n,a=r.alternate,a!==null&&(a.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(s=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,s&&e!==null&&(s=31-me(n),t=r.hiddenUpdates,a=t[s],a===null?t[s]=[e]:a.push(e),e.lane=n|536870912),r):null}function Gl(t){if(50<Qi)throw Qi=0,qo=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ra={};function Yg(t,e,n,a){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pe(t,e,n,a){return new Yg(t,e,n,a)}function _u(t){return t=t.prototype,!(!t||!t.isReactComponent)}function We(t,e){var n=t.alternate;return n===null?(n=pe(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Uf(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Xl(t,e,n,a,s,r){var f=0;if(a=t,typeof t=="function")_u(t)&&(f=1);else if(typeof t=="string")f=Xv(t,n,P.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case At:return t=pe(31,n,e,s),t.elementType=At,t.lanes=r,t;case B:return kn(n.children,s,r,e);case L:f=8,s|=24;break;case Y:return t=pe(12,n,e,s|2),t.elementType=Y,t.lanes=r,t;case q:return t=pe(13,n,e,s),t.elementType=q,t.lanes=r,t;case lt:return t=pe(19,n,e,s),t.elementType=lt,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case G:case H:f=10;break t;case J:f=9;break t;case it:f=11;break t;case ft:f=14;break t;case W:f=16,a=null;break t}f=29,n=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=pe(f,n,e,s),e.elementType=t,e.type=a,e.lanes=r,e}function kn(t,e,n,a){return t=pe(7,t,a,e),t.lanes=n,t}function wu(t,e,n){return t=pe(6,t,null,e),t.lanes=n,t}function Uu(t,e,n){return e=pe(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Oa=[],Na=0,Zl=null,Ql=0,De=[],Re=0,Jn=null,$e=1,Ie="";function Pn(t,e){Oa[Na++]=Ql,Oa[Na++]=Zl,Zl=t,Ql=e}function Bf(t,e,n){De[Re++]=$e,De[Re++]=Ie,De[Re++]=Jn,Jn=t;var a=$e;t=Ie;var s=32-me(a)-1;a&=~(1<<s),n+=1;var r=32-me(e)+s;if(30<r){var f=s-s%5;r=(a&(1<<f)-1).toString(32),a>>=f,s-=f,$e=1<<32-me(e)+s|n<<s|a,Ie=r+t}else $e=1<<r|n<<s|a,Ie=t}function Bu(t){t.return!==null&&(Pn(t,1),Bf(t,1,0))}function Lu(t){for(;t===Zl;)Zl=Oa[--Na],Oa[Na]=null,Ql=Oa[--Na],Oa[Na]=null;for(;t===Jn;)Jn=De[--Re],De[Re]=null,Ie=De[--Re],De[Re]=null,$e=De[--Re],De[Re]=null}var ie=null,Nt=null,yt=!1,Fn=null,qe=!1,Hu=Error(o(519));function Wn(t){var e=Error(o(418,""));throw Ai(Me(e,t)),Hu}function Lf(t){var e=t.stateNode,n=t.type,a=t.memoizedProps;switch(e[te]=t,e[se]=a,n){case"dialog":rt("cancel",e),rt("close",e);break;case"iframe":case"object":case"embed":rt("load",e);break;case"video":case"audio":for(n=0;n<ki.length;n++)rt(ki[n],e);break;case"source":rt("error",e);break;case"img":case"image":case"link":rt("error",e),rt("load",e);break;case"details":rt("toggle",e);break;case"input":rt("invalid",e),$c(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Cl(e);break;case"select":rt("invalid",e);break;case"textarea":rt("invalid",e),tf(e,a.value,a.defaultValue,a.children),Cl(e)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||a.suppressHydrationWarning===!0||em(e.textContent,n)?(a.popover!=null&&(rt("beforetoggle",e),rt("toggle",e)),a.onScroll!=null&&rt("scroll",e),a.onScrollEnd!=null&&rt("scrollend",e),a.onClick!=null&&(e.onclick=Es),e=!0):e=!1,e||Wn(t)}function Hf(t){for(ie=t.return;ie;)switch(ie.tag){case 5:case 13:qe=!1;return;case 27:case 3:qe=!0;return;default:ie=ie.return}}function Si(t){if(t!==ie)return!1;if(!yt)return Hf(t),yt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||nr(t.type,t.memoizedProps)),n=!n),n&&Nt&&Wn(t),Hf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Nt=Ue(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Nt=null}}else e===27?(e=Nt,jn(t.type)?(t=sr,sr=null,Nt=t):Nt=e):Nt=ie?Ue(t.stateNode.nextSibling):null;return!0}function Ti(){Nt=ie=null,yt=!1}function qf(){var t=Fn;return t!==null&&(ce===null?ce=t:ce.push.apply(ce,t),Fn=null),t}function Ai(t){Fn===null?Fn=[t]:Fn.push(t)}var qu=w(null),$n=null,tn=null;function gn(t,e,n){X(qu,e._currentValue),e._currentValue=n}function en(t){t._currentValue=qu.current,Z(qu)}function Yu(t,e,n){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===n)break;t=t.return}}function Gu(t,e,n,a){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var r=s.dependencies;if(r!==null){var f=s.child;r=r.firstContext;t:for(;r!==null;){var p=r;r=s;for(var b=0;b<e.length;b++)if(p.context===e[b]){r.lanes|=n,p=r.alternate,p!==null&&(p.lanes|=n),Yu(r.return,n,t),a||(f=null);break t}r=p.next}}else if(s.tag===18){if(f=s.return,f===null)throw Error(o(341));f.lanes|=n,r=f.alternate,r!==null&&(r.lanes|=n),Yu(f,n,t),f=null}else f=s.child;if(f!==null)f.return=s;else for(f=s;f!==null;){if(f===t){f=null;break}if(s=f.sibling,s!==null){s.return=f.return,f=s;break}f=f.return}s=f}}function Mi(t,e,n,a){t=null;for(var s=e,r=!1;s!==null;){if(!r){if((s.flags&524288)!==0)r=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var f=s.alternate;if(f===null)throw Error(o(387));if(f=f.memoizedProps,f!==null){var p=s.type;ye(s.pendingProps.value,f.value)||(t!==null?t.push(p):t=[p])}}else if(s===he.current){if(f=s.alternate,f===null)throw Error(o(387));f.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(Ii):t=[Ii])}s=s.return}t!==null&&Gu(e,t,n,a),e.flags|=262144}function Kl(t){for(t=t.firstContext;t!==null;){if(!ye(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function In(t){$n=t,tn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ee(t){return Yf($n,t)}function kl(t,e){return $n===null&&In(t),Yf(t,e)}function Yf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},tn===null){if(t===null)throw Error(o(308));tn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else tn=tn.next=e;return n}var Gg=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Xg=i.unstable_scheduleCallback,Zg=i.unstable_NormalPriority,qt={$$typeof:H,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Xu(){return{controller:new Gg,data:new Map,refCount:0}}function Ei(t){t.refCount--,t.refCount===0&&Xg(Zg,function(){t.controller.abort()})}var Di=null,Zu=0,Va=0,Ca=null;function Qg(t,e){if(Di===null){var n=Di=[];Zu=0,Va=ko(),Ca={status:"pending",value:void 0,then:function(a){n.push(a)}}}return Zu++,e.then(Gf,Gf),e}function Gf(){if(--Zu===0&&Di!==null){Ca!==null&&(Ca.status="fulfilled");var t=Di;Di=null,Va=0,Ca=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Kg(t,e){var n=[],a={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var s=0;s<n.length;s++)(0,n[s])(e)},function(s){for(a.status="rejected",a.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),a}var Xf=j.S;j.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Qg(t,e),Xf!==null&&Xf(t,e)};var ta=w(null);function Qu(){var t=ta.current;return t!==null?t:Tt.pooledCache}function Jl(t,e){e===null?X(ta,ta.current):X(ta,e.pool)}function Zf(){var t=Qu();return t===null?null:{parent:qt._currentValue,pool:t}}var Ri=Error(o(460)),Qf=Error(o(474)),Pl=Error(o(542)),Ku={then:function(){}};function Kf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Fl(){}function kf(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Fl,Fl),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Pf(t),t;default:if(typeof e.status=="string")e.then(Fl,Fl);else{if(t=Tt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=a}},function(a){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Pf(t),t}throw Oi=e,Ri}}var Oi=null;function Jf(){if(Oi===null)throw Error(o(459));var t=Oi;return Oi=null,t}function Pf(t){if(t===Ri||t===Pl)throw Error(o(483))}var vn=!1;function ku(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ju(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function bn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function xn(t,e,n){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(pt&2)!==0){var s=a.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),a.pending=e,e=Gl(t),wf(t,null,n),e}return Yl(t,a,e,n),Gl(t)}function Ni(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,n|=a,e.lanes=n,Yc(t,n)}}function Pu(t,e){var n=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var s=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?s=r=f:r=r.next=f,n=n.next}while(n!==null);r===null?s=r=e:r=r.next=e}else s=r=e;n={baseState:a.baseState,firstBaseUpdate:s,lastBaseUpdate:r,shared:a.shared,callbacks:a.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Fu=!1;function Vi(){if(Fu){var t=Ca;if(t!==null)throw t}}function Ci(t,e,n,a){Fu=!1;var s=t.updateQueue;vn=!1;var r=s.firstBaseUpdate,f=s.lastBaseUpdate,p=s.shared.pending;if(p!==null){s.shared.pending=null;var b=p,D=b.next;b.next=null,f===null?r=D:f.next=D,f=b;var C=t.alternate;C!==null&&(C=C.updateQueue,p=C.lastBaseUpdate,p!==f&&(p===null?C.firstBaseUpdate=D:p.next=D,C.lastBaseUpdate=b))}if(r!==null){var _=s.baseState;f=0,C=D=b=null,p=r;do{var O=p.lane&-536870913,N=O!==p.lane;if(N?(ct&O)===O:(a&O)===O){O!==0&&O===Va&&(Fu=!0),C!==null&&(C=C.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});t:{var I=t,F=p;O=e;var xt=n;switch(F.tag){case 1:if(I=F.payload,typeof I=="function"){_=I.call(xt,_,O);break t}_=I;break t;case 3:I.flags=I.flags&-65537|128;case 0:if(I=F.payload,O=typeof I=="function"?I.call(xt,_,O):I,O==null)break t;_=v({},_,O);break t;case 2:vn=!0}}O=p.callback,O!==null&&(t.flags|=64,N&&(t.flags|=8192),N=s.callbacks,N===null?s.callbacks=[O]:N.push(O))}else N={lane:O,tag:p.tag,payload:p.payload,callback:p.callback,next:null},C===null?(D=C=N,b=_):C=C.next=N,f|=O;if(p=p.next,p===null){if(p=s.shared.pending,p===null)break;N=p,p=N.next,N.next=null,s.lastBaseUpdate=N,s.shared.pending=null}}while(!0);C===null&&(b=_),s.baseState=b,s.firstBaseUpdate=D,s.lastBaseUpdate=C,r===null&&(s.shared.lanes=0),On|=f,t.lanes=f,t.memoizedState=_}}function Ff(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function Wf(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Ff(n[t],e)}var ja=w(null),Wl=w(0);function $f(t,e){t=rn,X(Wl,t),X(ja,e),rn=t|e.baseLanes}function Wu(){X(Wl,rn),X(ja,ja.current)}function $u(){rn=Wl.current,Z(ja),Z(Wl)}var Sn=0,st=null,vt=null,wt=null,$l=!1,za=!1,ea=!1,Il=0,ji=0,_a=null,kg=0;function jt(){throw Error(o(321))}function Iu(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!ye(t[n],e[n]))return!1;return!0}function to(t,e,n,a,s,r){return Sn=r,st=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,j.H=t===null||t.memoizedState===null?wh:Uh,ea=!1,r=n(a,s),ea=!1,za&&(r=th(e,n,a,s)),If(t),r}function If(t){j.H=ls;var e=vt!==null&&vt.next!==null;if(Sn=0,wt=vt=st=null,$l=!1,ji=0,_a=null,e)throw Error(o(300));t===null||Zt||(t=t.dependencies,t!==null&&Kl(t)&&(Zt=!0))}function th(t,e,n,a){st=t;var s=0;do{if(za&&(_a=null),ji=0,za=!1,25<=s)throw Error(o(301));if(s+=1,wt=vt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}j.H=tv,r=e(n,a)}while(za);return r}function Jg(){var t=j.H,e=t.useState()[0];return e=typeof e.then=="function"?zi(e):e,t=t.useState()[0],(vt!==null?vt.memoizedState:null)!==t&&(st.flags|=1024),e}function eo(){var t=Il!==0;return Il=0,t}function no(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function ao(t){if($l){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}$l=!1}Sn=0,wt=vt=st=null,za=!1,ji=Il=0,_a=null}function oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return wt===null?st.memoizedState=wt=t:wt=wt.next=t,wt}function Ut(){if(vt===null){var t=st.alternate;t=t!==null?t.memoizedState:null}else t=vt.next;var e=wt===null?st.memoizedState:wt.next;if(e!==null)wt=e,vt=t;else{if(t===null)throw st.alternate===null?Error(o(467)):Error(o(310));vt=t,t={memoizedState:vt.memoizedState,baseState:vt.baseState,baseQueue:vt.baseQueue,queue:vt.queue,next:null},wt===null?st.memoizedState=wt=t:wt=wt.next=t}return wt}function io(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function zi(t){var e=ji;return ji+=1,_a===null&&(_a=[]),t=kf(_a,t,e),e=st,(wt===null?e.memoizedState:wt.next)===null&&(e=e.alternate,j.H=e===null||e.memoizedState===null?wh:Uh),t}function ts(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return zi(t);if(t.$$typeof===H)return ee(t)}throw Error(o(438,String(t)))}function lo(t){var e=null,n=st.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var a=st.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=io(),st.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),a=0;a<t;a++)n[a]=Lt;return e.index++,n}function nn(t,e){return typeof e=="function"?e(t):e}function es(t){var e=Ut();return so(e,vt,t)}function so(t,e,n){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=n;var s=t.baseQueue,r=a.pending;if(r!==null){if(s!==null){var f=s.next;s.next=r.next,r.next=f}e.baseQueue=s=r,a.pending=null}if(r=t.baseState,s===null)t.memoizedState=r;else{e=s.next;var p=f=null,b=null,D=e,C=!1;do{var _=D.lane&-536870913;if(_!==D.lane?(ct&_)===_:(Sn&_)===_){var O=D.revertLane;if(O===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),_===Va&&(C=!0);else if((Sn&O)===O){D=D.next,O===Va&&(C=!0);continue}else _={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},b===null?(p=b=_,f=r):b=b.next=_,st.lanes|=O,On|=O;_=D.action,ea&&n(r,_),r=D.hasEagerState?D.eagerState:n(r,_)}else O={lane:_,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},b===null?(p=b=O,f=r):b=b.next=O,st.lanes|=_,On|=_;D=D.next}while(D!==null&&D!==e);if(b===null?f=r:b.next=p,!ye(r,t.memoizedState)&&(Zt=!0,C&&(n=Ca,n!==null)))throw n;t.memoizedState=r,t.baseState=f,t.baseQueue=b,a.lastRenderedState=r}return s===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function uo(t){var e=Ut(),n=e.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=t;var a=n.dispatch,s=n.pending,r=e.memoizedState;if(s!==null){n.pending=null;var f=s=s.next;do r=t(r,f.action),f=f.next;while(f!==s);ye(r,e.memoizedState)||(Zt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,a]}function eh(t,e,n){var a=st,s=Ut(),r=yt;if(r){if(n===void 0)throw Error(o(407));n=n()}else n=e();var f=!ye((vt||s).memoizedState,n);f&&(s.memoizedState=n,Zt=!0),s=s.queue;var p=ih.bind(null,a,s,t);if(_i(2048,8,p,[t]),s.getSnapshot!==e||f||wt!==null&&wt.memoizedState.tag&1){if(a.flags|=2048,wa(9,ns(),ah.bind(null,a,s,n,e),null),Tt===null)throw Error(o(349));r||(Sn&124)!==0||nh(a,e,n)}return n}function nh(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=st.updateQueue,e===null?(e=io(),st.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function ah(t,e,n,a){e.value=n,e.getSnapshot=a,lh(e)&&sh(t)}function ih(t,e,n){return n(function(){lh(e)&&sh(t)})}function lh(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!ye(t,n)}catch{return!0}}function sh(t){var e=Da(t,2);e!==null&&Se(e,t,2)}function oo(t){var e=oe();if(typeof t=="function"){var n=t;if(t=n(),ea){mn(!0);try{n()}finally{mn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:t},e}function uh(t,e,n,a){return t.baseState=n,so(t,vt,typeof a=="function"?a:nn)}function Pg(t,e,n,a,s){if(is(t))throw Error(o(485));if(t=e.action,t!==null){var r={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){r.listeners.push(f)}};j.T!==null?n(!0):r.isTransition=!1,a(r),n=e.pending,n===null?(r.next=e.pending=r,oh(e,r)):(r.next=n.next,e.pending=n.next=r)}}function oh(t,e){var n=e.action,a=e.payload,s=t.state;if(e.isTransition){var r=j.T,f={};j.T=f;try{var p=n(s,a),b=j.S;b!==null&&b(f,p),rh(t,e,p)}catch(D){ro(t,e,D)}finally{j.T=r}}else try{r=n(s,a),rh(t,e,r)}catch(D){ro(t,e,D)}}function rh(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){ch(t,e,a)},function(a){return ro(t,e,a)}):ch(t,e,n)}function ch(t,e,n){e.status="fulfilled",e.value=n,fh(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,oh(t,n)))}function ro(t,e,n){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=n,fh(e),e=e.next;while(e!==a)}t.action=null}function fh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function hh(t,e){return e}function dh(t,e){if(yt){var n=Tt.formState;if(n!==null){t:{var a=st;if(yt){if(Nt){e:{for(var s=Nt,r=qe;s.nodeType!==8;){if(!r){s=null;break e}if(s=Ue(s.nextSibling),s===null){s=null;break e}}r=s.data,s=r==="F!"||r==="F"?s:null}if(s){Nt=Ue(s.nextSibling),a=s.data==="F!";break t}}Wn(a)}a=!1}a&&(e=n[0])}}return n=oe(),n.memoizedState=n.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:hh,lastRenderedState:e},n.queue=a,n=jh.bind(null,st,a),a.dispatch=n,a=oo(!1),r=yo.bind(null,st,!1,a.queue),a=oe(),s={state:e,dispatch:null,action:t,pending:null},a.queue=s,n=Pg.bind(null,st,s,r,n),s.dispatch=n,a.memoizedState=t,[e,n,!1]}function mh(t){var e=Ut();return yh(e,vt,t)}function yh(t,e,n){if(e=so(t,e,hh)[0],t=es(nn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=zi(e)}catch(f){throw f===Ri?Pl:f}else a=e;e=Ut();var s=e.queue,r=s.dispatch;return n!==e.memoizedState&&(st.flags|=2048,wa(9,ns(),Fg.bind(null,s,n),null)),[a,r,t]}function Fg(t,e){t.action=e}function ph(t){var e=Ut(),n=vt;if(n!==null)return yh(e,n,t);Ut(),e=e.memoizedState,n=Ut();var a=n.queue.dispatch;return n.memoizedState=t,[e,a,!1]}function wa(t,e,n,a){return t={tag:t,create:n,deps:a,inst:e,next:null},e=st.updateQueue,e===null&&(e=io(),st.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(a=n.next,n.next=t,t.next=a,e.lastEffect=t),t}function ns(){return{destroy:void 0,resource:void 0}}function gh(){return Ut().memoizedState}function as(t,e,n,a){var s=oe();a=a===void 0?null:a,st.flags|=t,s.memoizedState=wa(1|e,ns(),n,a)}function _i(t,e,n,a){var s=Ut();a=a===void 0?null:a;var r=s.memoizedState.inst;vt!==null&&a!==null&&Iu(a,vt.memoizedState.deps)?s.memoizedState=wa(e,r,n,a):(st.flags|=t,s.memoizedState=wa(1|e,r,n,a))}function vh(t,e){as(8390656,8,t,e)}function bh(t,e){_i(2048,8,t,e)}function xh(t,e){return _i(4,2,t,e)}function Sh(t,e){return _i(4,4,t,e)}function Th(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Ah(t,e,n){n=n!=null?n.concat([t]):null,_i(4,4,Th.bind(null,e,t),n)}function co(){}function Mh(t,e){var n=Ut();e=e===void 0?null:e;var a=n.memoizedState;return e!==null&&Iu(e,a[1])?a[0]:(n.memoizedState=[t,e],t)}function Eh(t,e){var n=Ut();e=e===void 0?null:e;var a=n.memoizedState;if(e!==null&&Iu(e,a[1]))return a[0];if(a=t(),ea){mn(!0);try{t()}finally{mn(!1)}}return n.memoizedState=[a,e],a}function fo(t,e,n){return n===void 0||(Sn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Od(),st.lanes|=t,On|=t,n)}function Dh(t,e,n,a){return ye(n,e)?n:ja.current!==null?(t=fo(t,n,a),ye(t,e)||(Zt=!0),t):(Sn&42)===0?(Zt=!0,t.memoizedState=n):(t=Od(),st.lanes|=t,On|=t,e)}function Rh(t,e,n,a,s){var r=U.p;U.p=r!==0&&8>r?r:8;var f=j.T,p={};j.T=p,yo(t,!1,e,n);try{var b=s(),D=j.S;if(D!==null&&D(p,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var C=Kg(b,a);wi(t,e,C,xe(t))}else wi(t,e,a,xe(t))}catch(_){wi(t,e,{then:function(){},status:"rejected",reason:_},xe())}finally{U.p=r,j.T=f}}function Wg(){}function ho(t,e,n,a){if(t.tag!==5)throw Error(o(476));var s=Oh(t).queue;Rh(t,s,e,K,n===null?Wg:function(){return Nh(t),n(a)})}function Oh(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:K,baseState:K,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:K},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Nh(t){var e=Oh(t).next.queue;wi(t,e,{},xe())}function mo(){return ee(Ii)}function Vh(){return Ut().memoizedState}function Ch(){return Ut().memoizedState}function $g(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=xe();t=bn(n);var a=xn(e,t,n);a!==null&&(Se(a,e,n),Ni(a,e,n)),e={cache:Xu()},t.payload=e;return}e=e.return}}function Ig(t,e,n){var a=xe();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},is(t)?zh(e,n):(n=zu(t,e,n,a),n!==null&&(Se(n,t,a),_h(n,e,a)))}function jh(t,e,n){var a=xe();wi(t,e,n,a)}function wi(t,e,n,a){var s={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(is(t))zh(e,s);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var f=e.lastRenderedState,p=r(f,n);if(s.hasEagerState=!0,s.eagerState=p,ye(p,f))return Yl(t,e,s,0),Tt===null&&ql(),!1}catch{}finally{}if(n=zu(t,e,s,a),n!==null)return Se(n,t,a),_h(n,e,a),!0}return!1}function yo(t,e,n,a){if(a={lane:2,revertLane:ko(),action:a,hasEagerState:!1,eagerState:null,next:null},is(t)){if(e)throw Error(o(479))}else e=zu(t,n,a,2),e!==null&&Se(e,t,2)}function is(t){var e=t.alternate;return t===st||e!==null&&e===st}function zh(t,e){za=$l=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function _h(t,e,n){if((n&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,n|=a,e.lanes=n,Yc(t,n)}}var ls={readContext:ee,use:ts,useCallback:jt,useContext:jt,useEffect:jt,useImperativeHandle:jt,useLayoutEffect:jt,useInsertionEffect:jt,useMemo:jt,useReducer:jt,useRef:jt,useState:jt,useDebugValue:jt,useDeferredValue:jt,useTransition:jt,useSyncExternalStore:jt,useId:jt,useHostTransitionStatus:jt,useFormState:jt,useActionState:jt,useOptimistic:jt,useMemoCache:jt,useCacheRefresh:jt},wh={readContext:ee,use:ts,useCallback:function(t,e){return oe().memoizedState=[t,e===void 0?null:e],t},useContext:ee,useEffect:vh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,as(4194308,4,Th.bind(null,e,t),n)},useLayoutEffect:function(t,e){return as(4194308,4,t,e)},useInsertionEffect:function(t,e){as(4,2,t,e)},useMemo:function(t,e){var n=oe();e=e===void 0?null:e;var a=t();if(ea){mn(!0);try{t()}finally{mn(!1)}}return n.memoizedState=[a,e],a},useReducer:function(t,e,n){var a=oe();if(n!==void 0){var s=n(e);if(ea){mn(!0);try{n(e)}finally{mn(!1)}}}else s=e;return a.memoizedState=a.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},a.queue=t,t=t.dispatch=Ig.bind(null,st,t),[a.memoizedState,t]},useRef:function(t){var e=oe();return t={current:t},e.memoizedState=t},useState:function(t){t=oo(t);var e=t.queue,n=jh.bind(null,st,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:co,useDeferredValue:function(t,e){var n=oe();return fo(n,t,e)},useTransition:function(){var t=oo(!1);return t=Rh.bind(null,st,t.queue,!0,!1),oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var a=st,s=oe();if(yt){if(n===void 0)throw Error(o(407));n=n()}else{if(n=e(),Tt===null)throw Error(o(349));(ct&124)!==0||nh(a,e,n)}s.memoizedState=n;var r={value:n,getSnapshot:e};return s.queue=r,vh(ih.bind(null,a,r,t),[t]),a.flags|=2048,wa(9,ns(),ah.bind(null,a,r,n,e),null),n},useId:function(){var t=oe(),e=Tt.identifierPrefix;if(yt){var n=Ie,a=$e;n=(a&~(1<<32-me(a)-1)).toString(32)+n,e="«"+e+"R"+n,n=Il++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=kg++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:mo,useFormState:dh,useActionState:dh,useOptimistic:function(t){var e=oe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=yo.bind(null,st,!0,n),n.dispatch=e,[t,e]},useMemoCache:lo,useCacheRefresh:function(){return oe().memoizedState=$g.bind(null,st)}},Uh={readContext:ee,use:ts,useCallback:Mh,useContext:ee,useEffect:bh,useImperativeHandle:Ah,useInsertionEffect:xh,useLayoutEffect:Sh,useMemo:Eh,useReducer:es,useRef:gh,useState:function(){return es(nn)},useDebugValue:co,useDeferredValue:function(t,e){var n=Ut();return Dh(n,vt.memoizedState,t,e)},useTransition:function(){var t=es(nn)[0],e=Ut().memoizedState;return[typeof t=="boolean"?t:zi(t),e]},useSyncExternalStore:eh,useId:Vh,useHostTransitionStatus:mo,useFormState:mh,useActionState:mh,useOptimistic:function(t,e){var n=Ut();return uh(n,vt,t,e)},useMemoCache:lo,useCacheRefresh:Ch},tv={readContext:ee,use:ts,useCallback:Mh,useContext:ee,useEffect:bh,useImperativeHandle:Ah,useInsertionEffect:xh,useLayoutEffect:Sh,useMemo:Eh,useReducer:uo,useRef:gh,useState:function(){return uo(nn)},useDebugValue:co,useDeferredValue:function(t,e){var n=Ut();return vt===null?fo(n,t,e):Dh(n,vt.memoizedState,t,e)},useTransition:function(){var t=uo(nn)[0],e=Ut().memoizedState;return[typeof t=="boolean"?t:zi(t),e]},useSyncExternalStore:eh,useId:Vh,useHostTransitionStatus:mo,useFormState:ph,useActionState:ph,useOptimistic:function(t,e){var n=Ut();return vt!==null?uh(n,vt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:lo,useCacheRefresh:Ch},Ua=null,Ui=0;function ss(t){var e=Ui;return Ui+=1,Ua===null&&(Ua=[]),kf(Ua,t,e)}function Bi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function us(t,e){throw e.$$typeof===x?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Bh(t){var e=t._init;return e(t._payload)}function Lh(t){function e(A,T){if(t){var E=A.deletions;E===null?(A.deletions=[T],A.flags|=16):E.push(T)}}function n(A,T){if(!t)return null;for(;T!==null;)e(A,T),T=T.sibling;return null}function a(A){for(var T=new Map;A!==null;)A.key!==null?T.set(A.key,A):T.set(A.index,A),A=A.sibling;return T}function s(A,T){return A=We(A,T),A.index=0,A.sibling=null,A}function r(A,T,E){return A.index=E,t?(E=A.alternate,E!==null?(E=E.index,E<T?(A.flags|=67108866,T):E):(A.flags|=67108866,T)):(A.flags|=1048576,T)}function f(A){return t&&A.alternate===null&&(A.flags|=67108866),A}function p(A,T,E,z){return T===null||T.tag!==6?(T=wu(E,A.mode,z),T.return=A,T):(T=s(T,E),T.return=A,T)}function b(A,T,E,z){var Q=E.type;return Q===B?C(A,T,E.props.children,z,E.key):T!==null&&(T.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===W&&Bh(Q)===T.type)?(T=s(T,E.props),Bi(T,E),T.return=A,T):(T=Xl(E.type,E.key,E.props,null,A.mode,z),Bi(T,E),T.return=A,T)}function D(A,T,E,z){return T===null||T.tag!==4||T.stateNode.containerInfo!==E.containerInfo||T.stateNode.implementation!==E.implementation?(T=Uu(E,A.mode,z),T.return=A,T):(T=s(T,E.children||[]),T.return=A,T)}function C(A,T,E,z,Q){return T===null||T.tag!==7?(T=kn(E,A.mode,z,Q),T.return=A,T):(T=s(T,E),T.return=A,T)}function _(A,T,E){if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return T=wu(""+T,A.mode,E),T.return=A,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case M:return E=Xl(T.type,T.key,T.props,null,A.mode,E),Bi(E,T),E.return=A,E;case V:return T=Uu(T,A.mode,E),T.return=A,T;case W:var z=T._init;return T=z(T._payload),_(A,T,E)}if(Jt(T)||Ht(T))return T=kn(T,A.mode,E,null),T.return=A,T;if(typeof T.then=="function")return _(A,ss(T),E);if(T.$$typeof===H)return _(A,kl(A,T),E);us(A,T)}return null}function O(A,T,E,z){var Q=T!==null?T.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return Q!==null?null:p(A,T,""+E,z);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case M:return E.key===Q?b(A,T,E,z):null;case V:return E.key===Q?D(A,T,E,z):null;case W:return Q=E._init,E=Q(E._payload),O(A,T,E,z)}if(Jt(E)||Ht(E))return Q!==null?null:C(A,T,E,z,null);if(typeof E.then=="function")return O(A,T,ss(E),z);if(E.$$typeof===H)return O(A,T,kl(A,E),z);us(A,E)}return null}function N(A,T,E,z,Q){if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return A=A.get(E)||null,p(T,A,""+z,Q);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case M:return A=A.get(z.key===null?E:z.key)||null,b(T,A,z,Q);case V:return A=A.get(z.key===null?E:z.key)||null,D(T,A,z,Q);case W:var ut=z._init;return z=ut(z._payload),N(A,T,E,z,Q)}if(Jt(z)||Ht(z))return A=A.get(E)||null,C(T,A,z,Q,null);if(typeof z.then=="function")return N(A,T,E,ss(z),Q);if(z.$$typeof===H)return N(A,T,E,kl(T,z),Q);us(T,z)}return null}function I(A,T,E,z){for(var Q=null,ut=null,k=T,$=T=0,Kt=null;k!==null&&$<E.length;$++){k.index>$?(Kt=k,k=null):Kt=k.sibling;var dt=O(A,k,E[$],z);if(dt===null){k===null&&(k=Kt);break}t&&k&&dt.alternate===null&&e(A,k),T=r(dt,T,$),ut===null?Q=dt:ut.sibling=dt,ut=dt,k=Kt}if($===E.length)return n(A,k),yt&&Pn(A,$),Q;if(k===null){for(;$<E.length;$++)k=_(A,E[$],z),k!==null&&(T=r(k,T,$),ut===null?Q=k:ut.sibling=k,ut=k);return yt&&Pn(A,$),Q}for(k=a(k);$<E.length;$++)Kt=N(k,A,$,E[$],z),Kt!==null&&(t&&Kt.alternate!==null&&k.delete(Kt.key===null?$:Kt.key),T=r(Kt,T,$),ut===null?Q=Kt:ut.sibling=Kt,ut=Kt);return t&&k.forEach(function(Bn){return e(A,Bn)}),yt&&Pn(A,$),Q}function F(A,T,E,z){if(E==null)throw Error(o(151));for(var Q=null,ut=null,k=T,$=T=0,Kt=null,dt=E.next();k!==null&&!dt.done;$++,dt=E.next()){k.index>$?(Kt=k,k=null):Kt=k.sibling;var Bn=O(A,k,dt.value,z);if(Bn===null){k===null&&(k=Kt);break}t&&k&&Bn.alternate===null&&e(A,k),T=r(Bn,T,$),ut===null?Q=Bn:ut.sibling=Bn,ut=Bn,k=Kt}if(dt.done)return n(A,k),yt&&Pn(A,$),Q;if(k===null){for(;!dt.done;$++,dt=E.next())dt=_(A,dt.value,z),dt!==null&&(T=r(dt,T,$),ut===null?Q=dt:ut.sibling=dt,ut=dt);return yt&&Pn(A,$),Q}for(k=a(k);!dt.done;$++,dt=E.next())dt=N(k,A,$,dt.value,z),dt!==null&&(t&&dt.alternate!==null&&k.delete(dt.key===null?$:dt.key),T=r(dt,T,$),ut===null?Q=dt:ut.sibling=dt,ut=dt);return t&&k.forEach(function(e1){return e(A,e1)}),yt&&Pn(A,$),Q}function xt(A,T,E,z){if(typeof E=="object"&&E!==null&&E.type===B&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case M:t:{for(var Q=E.key;T!==null;){if(T.key===Q){if(Q=E.type,Q===B){if(T.tag===7){n(A,T.sibling),z=s(T,E.props.children),z.return=A,A=z;break t}}else if(T.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===W&&Bh(Q)===T.type){n(A,T.sibling),z=s(T,E.props),Bi(z,E),z.return=A,A=z;break t}n(A,T);break}else e(A,T);T=T.sibling}E.type===B?(z=kn(E.props.children,A.mode,z,E.key),z.return=A,A=z):(z=Xl(E.type,E.key,E.props,null,A.mode,z),Bi(z,E),z.return=A,A=z)}return f(A);case V:t:{for(Q=E.key;T!==null;){if(T.key===Q)if(T.tag===4&&T.stateNode.containerInfo===E.containerInfo&&T.stateNode.implementation===E.implementation){n(A,T.sibling),z=s(T,E.children||[]),z.return=A,A=z;break t}else{n(A,T);break}else e(A,T);T=T.sibling}z=Uu(E,A.mode,z),z.return=A,A=z}return f(A);case W:return Q=E._init,E=Q(E._payload),xt(A,T,E,z)}if(Jt(E))return I(A,T,E,z);if(Ht(E)){if(Q=Ht(E),typeof Q!="function")throw Error(o(150));return E=Q.call(E),F(A,T,E,z)}if(typeof E.then=="function")return xt(A,T,ss(E),z);if(E.$$typeof===H)return xt(A,T,kl(A,E),z);us(A,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,T!==null&&T.tag===6?(n(A,T.sibling),z=s(T,E),z.return=A,A=z):(n(A,T),z=wu(E,A.mode,z),z.return=A,A=z),f(A)):n(A,T)}return function(A,T,E,z){try{Ui=0;var Q=xt(A,T,E,z);return Ua=null,Q}catch(k){if(k===Ri||k===Pl)throw k;var ut=pe(29,k,null,A.mode);return ut.lanes=z,ut.return=A,ut}finally{}}}var Ba=Lh(!0),Hh=Lh(!1),Oe=w(null),Ye=null;function Tn(t){var e=t.alternate;X(Yt,Yt.current&1),X(Oe,t),Ye===null&&(e===null||ja.current!==null||e.memoizedState!==null)&&(Ye=t)}function qh(t){if(t.tag===22){if(X(Yt,Yt.current),X(Oe,t),Ye===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ye=t)}}else An()}function An(){X(Yt,Yt.current),X(Oe,Oe.current)}function an(t){Z(Oe),Ye===t&&(Ye=null),Z(Yt)}var Yt=w(0);function os(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||lr(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function po(t,e,n,a){e=t.memoizedState,n=n(a,e),n=n==null?e:v({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var go={enqueueSetState:function(t,e,n){t=t._reactInternals;var a=xe(),s=bn(a);s.payload=e,n!=null&&(s.callback=n),e=xn(t,s,a),e!==null&&(Se(e,t,a),Ni(e,t,a))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var a=xe(),s=bn(a);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=xn(t,s,a),e!==null&&(Se(e,t,a),Ni(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=xe(),a=bn(n);a.tag=2,e!=null&&(a.callback=e),e=xn(t,a,n),e!==null&&(Se(e,t,n),Ni(e,t,n))}};function Yh(t,e,n,a,s,r,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,r,f):e.prototype&&e.prototype.isPureReactComponent?!bi(n,a)||!bi(s,r):!0}function Gh(t,e,n,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,a),e.state!==t&&go.enqueueReplaceState(e,e.state,null)}function na(t,e){var n=e;if("ref"in e){n={};for(var a in e)a!=="ref"&&(n[a]=e[a])}if(t=t.defaultProps){n===e&&(n=v({},n));for(var s in t)n[s]===void 0&&(n[s]=t[s])}return n}var rs=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Xh(t){rs(t)}function Zh(t){console.error(t)}function Qh(t){rs(t)}function cs(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function Kh(t,e,n){try{var a=t.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function vo(t,e,n){return n=bn(n),n.tag=3,n.payload={element:null},n.callback=function(){cs(t,e)},n}function kh(t){return t=bn(t),t.tag=3,t}function Jh(t,e,n,a){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var r=a.value;t.payload=function(){return s(r)},t.callback=function(){Kh(e,n,a)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Kh(e,n,a),typeof s!="function"&&(Nn===null?Nn=new Set([this]):Nn.add(this));var p=a.stack;this.componentDidCatch(a.value,{componentStack:p!==null?p:""})})}function ev(t,e,n,a,s){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=n.alternate,e!==null&&Mi(e,n,s,!0),n=Oe.current,n!==null){switch(n.tag){case 13:return Ye===null?Go():n.alternate===null&&Vt===0&&(Vt=3),n.flags&=-257,n.flags|=65536,n.lanes=s,a===Ku?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([a]):e.add(a),Zo(t,a,s)),!1;case 22:return n.flags|=65536,a===Ku?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([a]):n.add(a)),Zo(t,a,s)),!1}throw Error(o(435,n.tag))}return Zo(t,a,s),Go(),!1}if(yt)return e=Oe.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,a!==Hu&&(t=Error(o(422),{cause:a}),Ai(Me(t,n)))):(a!==Hu&&(e=Error(o(423),{cause:a}),Ai(Me(e,n))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,a=Me(a,n),s=vo(t.stateNode,a,s),Pu(t,s),Vt!==4&&(Vt=2)),!1;var r=Error(o(520),{cause:a});if(r=Me(r,n),Zi===null?Zi=[r]:Zi.push(r),Vt!==4&&(Vt=2),e===null)return!0;a=Me(a,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=s&-s,n.lanes|=t,t=vo(n.stateNode,a,t),Pu(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Nn===null||!Nn.has(r))))return n.flags|=65536,s&=-s,n.lanes|=s,s=kh(s),Jh(s,t,n,a),Pu(n,s),!1}n=n.return}while(n!==null);return!1}var Ph=Error(o(461)),Zt=!1;function Pt(t,e,n,a){e.child=t===null?Hh(e,null,n,a):Ba(e,t.child,n,a)}function Fh(t,e,n,a,s){n=n.render;var r=e.ref;if("ref"in a){var f={};for(var p in a)p!=="ref"&&(f[p]=a[p])}else f=a;return In(e),a=to(t,e,n,f,r,s),p=eo(),t!==null&&!Zt?(no(t,e,s),ln(t,e,s)):(yt&&p&&Bu(e),e.flags|=1,Pt(t,e,a,s),e.child)}function Wh(t,e,n,a,s){if(t===null){var r=n.type;return typeof r=="function"&&!_u(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,$h(t,e,r,a,s)):(t=Xl(n.type,null,a,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!Do(t,s)){var f=r.memoizedProps;if(n=n.compare,n=n!==null?n:bi,n(f,a)&&t.ref===e.ref)return ln(t,e,s)}return e.flags|=1,t=We(r,a),t.ref=e.ref,t.return=e,e.child=t}function $h(t,e,n,a,s){if(t!==null){var r=t.memoizedProps;if(bi(r,a)&&t.ref===e.ref)if(Zt=!1,e.pendingProps=a=r,Do(t,s))(t.flags&131072)!==0&&(Zt=!0);else return e.lanes=t.lanes,ln(t,e,s)}return bo(t,e,n,a,s)}function Ih(t,e,n){var a=e.pendingProps,s=a.children,r=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=r!==null?r.baseLanes|n:n,t!==null){for(s=e.child=t.child,r=0;s!==null;)r=r|s.lanes|s.childLanes,s=s.sibling;e.childLanes=r&~a}else e.childLanes=0,e.child=null;return td(t,e,a,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Jl(e,r!==null?r.cachePool:null),r!==null?$f(e,r):Wu(),qh(e);else return e.lanes=e.childLanes=536870912,td(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(Jl(e,r.cachePool),$f(e,r),An(),e.memoizedState=null):(t!==null&&Jl(e,null),Wu(),An());return Pt(t,e,s,n),e.child}function td(t,e,n,a){var s=Qu();return s=s===null?null:{parent:qt._currentValue,pool:s},e.memoizedState={baseLanes:n,cachePool:s},t!==null&&Jl(e,null),Wu(),qh(e),t!==null&&Mi(t,e,a,!0),null}function fs(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function bo(t,e,n,a,s){return In(e),n=to(t,e,n,a,void 0,s),a=eo(),t!==null&&!Zt?(no(t,e,s),ln(t,e,s)):(yt&&a&&Bu(e),e.flags|=1,Pt(t,e,n,s),e.child)}function ed(t,e,n,a,s,r){return In(e),e.updateQueue=null,n=th(e,a,n,s),If(t),a=eo(),t!==null&&!Zt?(no(t,e,r),ln(t,e,r)):(yt&&a&&Bu(e),e.flags|=1,Pt(t,e,n,r),e.child)}function nd(t,e,n,a,s){if(In(e),e.stateNode===null){var r=Ra,f=n.contextType;typeof f=="object"&&f!==null&&(r=ee(f)),r=new n(a,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=go,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=a,r.state=e.memoizedState,r.refs={},ku(e),f=n.contextType,r.context=typeof f=="object"&&f!==null?ee(f):Ra,r.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(po(e,n,f,a),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(f=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),f!==r.state&&go.enqueueReplaceState(r,r.state,null),Ci(e,a,r,s),Vi(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){r=e.stateNode;var p=e.memoizedProps,b=na(n,p);r.props=b;var D=r.context,C=n.contextType;f=Ra,typeof C=="object"&&C!==null&&(f=ee(C));var _=n.getDerivedStateFromProps;C=typeof _=="function"||typeof r.getSnapshotBeforeUpdate=="function",p=e.pendingProps!==p,C||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(p||D!==f)&&Gh(e,r,a,f),vn=!1;var O=e.memoizedState;r.state=O,Ci(e,a,r,s),Vi(),D=e.memoizedState,p||O!==D||vn?(typeof _=="function"&&(po(e,n,_,a),D=e.memoizedState),(b=vn||Yh(e,n,b,a,O,D,f))?(C||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=D),r.props=a,r.state=D,r.context=f,a=b):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{r=e.stateNode,Ju(t,e),f=e.memoizedProps,C=na(n,f),r.props=C,_=e.pendingProps,O=r.context,D=n.contextType,b=Ra,typeof D=="object"&&D!==null&&(b=ee(D)),p=n.getDerivedStateFromProps,(D=typeof p=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(f!==_||O!==b)&&Gh(e,r,a,b),vn=!1,O=e.memoizedState,r.state=O,Ci(e,a,r,s),Vi();var N=e.memoizedState;f!==_||O!==N||vn||t!==null&&t.dependencies!==null&&Kl(t.dependencies)?(typeof p=="function"&&(po(e,n,p,a),N=e.memoizedState),(C=vn||Yh(e,n,C,a,O,N,b)||t!==null&&t.dependencies!==null&&Kl(t.dependencies))?(D||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(a,N,b),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(a,N,b)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=N),r.props=a,r.state=N,r.context=b,a=C):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&O===t.memoizedState||(e.flags|=1024),a=!1)}return r=a,fs(t,e),a=(e.flags&128)!==0,r||a?(r=e.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&a?(e.child=Ba(e,t.child,null,s),e.child=Ba(e,null,n,s)):Pt(t,e,n,s),e.memoizedState=r.state,t=e.child):t=ln(t,e,s),t}function ad(t,e,n,a){return Ti(),e.flags|=256,Pt(t,e,n,a),e.child}var xo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function So(t){return{baseLanes:t,cachePool:Zf()}}function To(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ne),t}function id(t,e,n){var a=e.pendingProps,s=!1,r=(e.flags&128)!==0,f;if((f=r)||(f=t!==null&&t.memoizedState===null?!1:(Yt.current&2)!==0),f&&(s=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(yt){if(s?Tn(e):An(),yt){var p=Nt,b;if(b=p){t:{for(b=p,p=qe;b.nodeType!==8;){if(!p){p=null;break t}if(b=Ue(b.nextSibling),b===null){p=null;break t}}p=b}p!==null?(e.memoizedState={dehydrated:p,treeContext:Jn!==null?{id:$e,overflow:Ie}:null,retryLane:536870912,hydrationErrors:null},b=pe(18,null,null,0),b.stateNode=p,b.return=e,e.child=b,ie=e,Nt=null,b=!0):b=!1}b||Wn(e)}if(p=e.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return lr(p)?e.lanes=32:e.lanes=536870912,null;an(e)}return p=a.children,a=a.fallback,s?(An(),s=e.mode,p=hs({mode:"hidden",children:p},s),a=kn(a,s,n,null),p.return=e,a.return=e,p.sibling=a,e.child=p,s=e.child,s.memoizedState=So(n),s.childLanes=To(t,f,n),e.memoizedState=xo,a):(Tn(e),Ao(e,p))}if(b=t.memoizedState,b!==null&&(p=b.dehydrated,p!==null)){if(r)e.flags&256?(Tn(e),e.flags&=-257,e=Mo(t,e,n)):e.memoizedState!==null?(An(),e.child=t.child,e.flags|=128,e=null):(An(),s=a.fallback,p=e.mode,a=hs({mode:"visible",children:a.children},p),s=kn(s,p,n,null),s.flags|=2,a.return=e,s.return=e,a.sibling=s,e.child=a,Ba(e,t.child,null,n),a=e.child,a.memoizedState=So(n),a.childLanes=To(t,f,n),e.memoizedState=xo,e=s);else if(Tn(e),lr(p)){if(f=p.nextSibling&&p.nextSibling.dataset,f)var D=f.dgst;f=D,a=Error(o(419)),a.stack="",a.digest=f,Ai({value:a,source:null,stack:null}),e=Mo(t,e,n)}else if(Zt||Mi(t,e,n,!1),f=(n&t.childLanes)!==0,Zt||f){if(f=Tt,f!==null&&(a=n&-n,a=(a&42)!==0?1:lu(a),a=(a&(f.suspendedLanes|n))!==0?0:a,a!==0&&a!==b.retryLane))throw b.retryLane=a,Da(t,a),Se(f,t,a),Ph;p.data==="$?"||Go(),e=Mo(t,e,n)}else p.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=b.treeContext,Nt=Ue(p.nextSibling),ie=e,yt=!0,Fn=null,qe=!1,t!==null&&(De[Re++]=$e,De[Re++]=Ie,De[Re++]=Jn,$e=t.id,Ie=t.overflow,Jn=e),e=Ao(e,a.children),e.flags|=4096);return e}return s?(An(),s=a.fallback,p=e.mode,b=t.child,D=b.sibling,a=We(b,{mode:"hidden",children:a.children}),a.subtreeFlags=b.subtreeFlags&65011712,D!==null?s=We(D,s):(s=kn(s,p,n,null),s.flags|=2),s.return=e,a.return=e,a.sibling=s,e.child=a,a=s,s=e.child,p=t.child.memoizedState,p===null?p=So(n):(b=p.cachePool,b!==null?(D=qt._currentValue,b=b.parent!==D?{parent:D,pool:D}:b):b=Zf(),p={baseLanes:p.baseLanes|n,cachePool:b}),s.memoizedState=p,s.childLanes=To(t,f,n),e.memoizedState=xo,a):(Tn(e),n=t.child,t=n.sibling,n=We(n,{mode:"visible",children:a.children}),n.return=e,n.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=n,e.memoizedState=null,n)}function Ao(t,e){return e=hs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function hs(t,e){return t=pe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Mo(t,e,n){return Ba(e,t.child,null,n),t=Ao(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function ld(t,e,n){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Yu(t.return,e,n)}function Eo(t,e,n,a,s){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:s}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=a,r.tail=n,r.tailMode=s)}function sd(t,e,n){var a=e.pendingProps,s=a.revealOrder,r=a.tail;if(Pt(t,e,a.children,n),a=Yt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&ld(t,n,e);else if(t.tag===19)ld(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(X(Yt,a),s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&os(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),Eo(e,!1,s,n,r);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&os(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}Eo(e,!0,n,null,r);break;case"together":Eo(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ln(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),On|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Mi(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,n=We(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=We(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Do(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Kl(t)))}function nv(t,e,n){switch(e.tag){case 3:Mt(e,e.stateNode.containerInfo),gn(e,qt,t.memoizedState.cache),Ti();break;case 27:case 5:tu(e);break;case 4:Mt(e,e.stateNode.containerInfo);break;case 10:gn(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(Tn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?id(t,e,n):(Tn(e),t=ln(t,e,n),t!==null?t.sibling:null);Tn(e);break;case 19:var s=(t.flags&128)!==0;if(a=(n&e.childLanes)!==0,a||(Mi(t,e,n,!1),a=(n&e.childLanes)!==0),s){if(a)return sd(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),X(Yt,Yt.current),a)break;return null;case 22:case 23:return e.lanes=0,Ih(t,e,n);case 24:gn(e,qt,t.memoizedState.cache)}return ln(t,e,n)}function ud(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Zt=!0;else{if(!Do(t,n)&&(e.flags&128)===0)return Zt=!1,nv(t,e,n);Zt=(t.flags&131072)!==0}else Zt=!1,yt&&(e.flags&1048576)!==0&&Bf(e,Ql,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,s=a._init;if(a=s(a._payload),e.type=a,typeof a=="function")_u(a)?(t=na(a,t),e.tag=1,e=nd(null,e,a,t,n)):(e.tag=0,e=bo(null,e,a,t,n));else{if(a!=null){if(s=a.$$typeof,s===it){e.tag=11,e=Fh(null,e,a,t,n);break t}else if(s===ft){e.tag=14,e=Wh(null,e,a,t,n);break t}}throw e=Le(a)||a,Error(o(306,e,""))}}return e;case 0:return bo(t,e,e.type,e.pendingProps,n);case 1:return a=e.type,s=na(a,e.pendingProps),nd(t,e,a,s,n);case 3:t:{if(Mt(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var r=e.memoizedState;s=r.element,Ju(t,e),Ci(e,a,null,n);var f=e.memoizedState;if(a=f.cache,gn(e,qt,a),a!==r.cache&&Gu(e,[qt],n,!0),Vi(),a=f.element,r.isDehydrated)if(r={element:a,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=ad(t,e,a,n);break t}else if(a!==s){s=Me(Error(o(424)),e),Ai(s),e=ad(t,e,a,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Nt=Ue(t.firstChild),ie=e,yt=!0,Fn=null,qe=!0,n=Hh(e,null,a,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Ti(),a===s){e=ln(t,e,n);break t}Pt(t,e,a,n)}e=e.child}return e;case 26:return fs(t,e),t===null?(n=fm(e.type,null,e.pendingProps,null))?e.memoizedState=n:yt||(n=e.type,t=e.pendingProps,a=Ds(nt.current).createElement(n),a[te]=e,a[se]=t,Wt(a,n,t),Xt(a),e.stateNode=a):e.memoizedState=fm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return tu(e),t===null&&yt&&(a=e.stateNode=om(e.type,e.pendingProps,nt.current),ie=e,qe=!0,s=Nt,jn(e.type)?(sr=s,Nt=Ue(a.firstChild)):Nt=s),Pt(t,e,e.pendingProps.children,n),fs(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&yt&&((s=a=Nt)&&(a=Vv(a,e.type,e.pendingProps,qe),a!==null?(e.stateNode=a,ie=e,Nt=Ue(a.firstChild),qe=!1,s=!0):s=!1),s||Wn(e)),tu(e),s=e.type,r=e.pendingProps,f=t!==null?t.memoizedProps:null,a=r.children,nr(s,r)?a=null:f!==null&&nr(s,f)&&(e.flags|=32),e.memoizedState!==null&&(s=to(t,e,Jg,null,null,n),Ii._currentValue=s),fs(t,e),Pt(t,e,a,n),e.child;case 6:return t===null&&yt&&((t=n=Nt)&&(n=Cv(n,e.pendingProps,qe),n!==null?(e.stateNode=n,ie=e,Nt=null,t=!0):t=!1),t||Wn(e)),null;case 13:return id(t,e,n);case 4:return Mt(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ba(e,null,a,n):Pt(t,e,a,n),e.child;case 11:return Fh(t,e,e.type,e.pendingProps,n);case 7:return Pt(t,e,e.pendingProps,n),e.child;case 8:return Pt(t,e,e.pendingProps.children,n),e.child;case 12:return Pt(t,e,e.pendingProps.children,n),e.child;case 10:return a=e.pendingProps,gn(e,e.type,a.value),Pt(t,e,a.children,n),e.child;case 9:return s=e.type._context,a=e.pendingProps.children,In(e),s=ee(s),a=a(s),e.flags|=1,Pt(t,e,a,n),e.child;case 14:return Wh(t,e,e.type,e.pendingProps,n);case 15:return $h(t,e,e.type,e.pendingProps,n);case 19:return sd(t,e,n);case 31:return a=e.pendingProps,n=e.mode,a={mode:a.mode,children:a.children},t===null?(n=hs(a,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=We(t.child,a),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Ih(t,e,n);case 24:return In(e),a=ee(qt),t===null?(s=Qu(),s===null&&(s=Tt,r=Xu(),s.pooledCache=r,r.refCount++,r!==null&&(s.pooledCacheLanes|=n),s=r),e.memoizedState={parent:a,cache:s},ku(e),gn(e,qt,s)):((t.lanes&n)!==0&&(Ju(t,e),Ci(e,null,null,n),Vi()),s=t.memoizedState,r=e.memoizedState,s.parent!==a?(s={parent:a,cache:a},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),gn(e,qt,a)):(a=r.cache,gn(e,qt,a),a!==s.cache&&Gu(e,[qt],n,!0))),Pt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function sn(t){t.flags|=4}function od(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!pm(e)){if(e=Oe.current,e!==null&&((ct&4194048)===ct?Ye!==null:(ct&62914560)!==ct&&(ct&536870912)===0||e!==Ye))throw Oi=Ku,Qf;t.flags|=8192}}function ds(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Hc():536870912,t.lanes|=e,Ya|=e)}function Li(t,e){if(!yt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Dt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,a=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags&65011712,a|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags,a|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=a,t.childLanes=n,e}function av(t,e,n){var a=e.pendingProps;switch(Lu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Dt(e),null;case 1:return Dt(e),null;case 3:return n=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),en(qt),dn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Si(e)?sn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,qf())),Dt(e),null;case 26:return n=e.memoizedState,t===null?(sn(e),n!==null?(Dt(e),od(e,n)):(Dt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(sn(e),Dt(e),od(e,n)):(Dt(e),e.flags&=-16777217):(t.memoizedProps!==a&&sn(e),Dt(e),e.flags&=-16777217),null;case 27:Ml(e),n=nt.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&sn(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Dt(e),null}t=P.current,Si(e)?Lf(e):(t=om(s,a,n),e.stateNode=t,sn(e))}return Dt(e),null;case 5:if(Ml(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&sn(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Dt(e),null}if(t=P.current,Si(e))Lf(e);else{switch(s=Ds(nt.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?s.createElement("select",{is:a.is}):s.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?s.createElement(n,{is:a.is}):s.createElement(n)}}t[te]=e,t[se]=a;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch(Wt(t,n,a),n){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&sn(e)}}return Dt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&sn(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=nt.current,Si(e)){if(t=e.stateNode,n=e.memoizedProps,a=null,s=ie,s!==null)switch(s.tag){case 27:case 5:a=s.memoizedProps}t[te]=e,t=!!(t.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||em(t.nodeValue,n)),t||Wn(e)}else t=Ds(t).createTextNode(a),t[te]=e,e.stateNode=t}return Dt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=Si(e),a!==null&&a.dehydrated!==null){if(t===null){if(!s)throw Error(o(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(o(317));s[te]=e}else Ti(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Dt(e),s=!1}else s=qf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(an(e),e):(an(e),null)}if(an(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=a!==null,t=t!==null&&t.memoizedState!==null,n){a=e.child,s=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(s=a.alternate.memoizedState.cachePool.pool);var r=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(r=a.memoizedState.cachePool.pool),r!==s&&(a.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),ds(e,e.updateQueue),Dt(e),null;case 4:return dn(),t===null&&Wo(e.stateNode.containerInfo),Dt(e),null;case 10:return en(e.type),Dt(e),null;case 19:if(Z(Yt),s=e.memoizedState,s===null)return Dt(e),null;if(a=(e.flags&128)!==0,r=s.rendering,r===null)if(a)Li(s,!1);else{if(Vt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(r=os(t),r!==null){for(e.flags|=128,Li(s,!1),t=r.updateQueue,e.updateQueue=t,ds(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Uf(n,t),n=n.sibling;return X(Yt,Yt.current&1|2),e.child}t=t.sibling}s.tail!==null&&He()>ps&&(e.flags|=128,a=!0,Li(s,!1),e.lanes=4194304)}else{if(!a)if(t=os(r),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ds(e,t),Li(s,!0),s.tail===null&&s.tailMode==="hidden"&&!r.alternate&&!yt)return Dt(e),null}else 2*He()-s.renderingStartTime>ps&&n!==536870912&&(e.flags|=128,a=!0,Li(s,!1),e.lanes=4194304);s.isBackwards?(r.sibling=e.child,e.child=r):(t=s.last,t!==null?t.sibling=r:e.child=r,s.last=r)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=He(),e.sibling=null,t=Yt.current,X(Yt,a?t&1|2:t&1),e):(Dt(e),null);case 22:case 23:return an(e),$u(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(n&536870912)!==0&&(e.flags&128)===0&&(Dt(e),e.subtreeFlags&6&&(e.flags|=8192)):Dt(e),n=e.updateQueue,n!==null&&ds(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==n&&(e.flags|=2048),t!==null&&Z(ta),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),en(qt),Dt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function iv(t,e){switch(Lu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return en(qt),dn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Ml(e),null;case 13:if(an(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));Ti()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Z(Yt),null;case 4:return dn(),null;case 10:return en(e.type),null;case 22:case 23:return an(e),$u(),t!==null&&Z(ta),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return en(qt),null;case 25:return null;default:return null}}function rd(t,e){switch(Lu(e),e.tag){case 3:en(qt),dn();break;case 26:case 27:case 5:Ml(e);break;case 4:dn();break;case 13:an(e);break;case 19:Z(Yt);break;case 10:en(e.type);break;case 22:case 23:an(e),$u(),t!==null&&Z(ta);break;case 24:en(qt)}}function Hi(t,e){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var s=a.next;n=s;do{if((n.tag&t)===t){a=void 0;var r=n.create,f=n.inst;a=r(),f.destroy=a}n=n.next}while(n!==s)}}catch(p){St(e,e.return,p)}}function Mn(t,e,n){try{var a=e.updateQueue,s=a!==null?a.lastEffect:null;if(s!==null){var r=s.next;a=r;do{if((a.tag&t)===t){var f=a.inst,p=f.destroy;if(p!==void 0){f.destroy=void 0,s=e;var b=n,D=p;try{D()}catch(C){St(s,b,C)}}}a=a.next}while(a!==r)}}catch(C){St(e,e.return,C)}}function cd(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Wf(e,n)}catch(a){St(t,t.return,a)}}}function fd(t,e,n){n.props=na(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(a){St(t,e,a)}}function qi(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof n=="function"?t.refCleanup=n(a):n.current=a}}catch(s){St(t,e,s)}}function Ge(t,e){var n=t.ref,a=t.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(s){St(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){St(t,e,s)}else n.current=null}function hd(t){var e=t.type,n=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break t;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(s){St(t,t.return,s)}}function Ro(t,e,n){try{var a=t.stateNode;Ev(a,t.type,n,e),a[se]=e}catch(s){St(t,t.return,s)}}function dd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&jn(t.type)||t.tag===4}function Oo(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||dd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&jn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function No(t,e,n){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Es));else if(a!==4&&(a===27&&jn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(No(t,e,n),t=t.sibling;t!==null;)No(t,e,n),t=t.sibling}function ms(t,e,n){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(a!==4&&(a===27&&jn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(ms(t,e,n),t=t.sibling;t!==null;)ms(t,e,n),t=t.sibling}function md(t){var e=t.stateNode,n=t.memoizedProps;try{for(var a=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);Wt(e,a,n),e[te]=t,e[se]=n}catch(r){St(t,t.return,r)}}var un=!1,zt=!1,Vo=!1,yd=typeof WeakSet=="function"?WeakSet:Set,Qt=null;function lv(t,e){if(t=t.containerInfo,tr=js,t=Df(t),Ru(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var s=a.anchorOffset,r=a.focusNode;a=a.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break t}var f=0,p=-1,b=-1,D=0,C=0,_=t,O=null;e:for(;;){for(var N;_!==n||s!==0&&_.nodeType!==3||(p=f+s),_!==r||a!==0&&_.nodeType!==3||(b=f+a),_.nodeType===3&&(f+=_.nodeValue.length),(N=_.firstChild)!==null;)O=_,_=N;for(;;){if(_===t)break e;if(O===n&&++D===s&&(p=f),O===r&&++C===a&&(b=f),(N=_.nextSibling)!==null)break;_=O,O=_.parentNode}_=N}n=p===-1||b===-1?null:{start:p,end:b}}else n=null}n=n||{start:0,end:0}}else n=null;for(er={focusedElem:t,selectionRange:n},js=!1,Qt=e;Qt!==null;)if(e=Qt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Qt=t;else for(;Qt!==null;){switch(e=Qt,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&r!==null){t=void 0,n=e,s=r.memoizedProps,r=r.memoizedState,a=n.stateNode;try{var I=na(n.type,s,n.elementType===n.type);t=a.getSnapshotBeforeUpdate(I,r),a.__reactInternalSnapshotBeforeUpdate=t}catch(F){St(n,n.return,F)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)ir(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":ir(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,Qt=t;break}Qt=e.return}}function pd(t,e,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:En(t,n),a&4&&Hi(5,n);break;case 1:if(En(t,n),a&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(f){St(n,n.return,f)}else{var s=na(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){St(n,n.return,f)}}a&64&&cd(n),a&512&&qi(n,n.return);break;case 3:if(En(t,n),a&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Wf(t,e)}catch(f){St(n,n.return,f)}}break;case 27:e===null&&a&4&&md(n);case 26:case 5:En(t,n),e===null&&a&4&&hd(n),a&512&&qi(n,n.return);break;case 12:En(t,n);break;case 13:En(t,n),a&4&&bd(t,n),a&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=mv.bind(null,n),jv(t,n))));break;case 22:if(a=n.memoizedState!==null||un,!a){e=e!==null&&e.memoizedState!==null||zt,s=un;var r=zt;un=a,(zt=e)&&!r?Dn(t,n,(n.subtreeFlags&8772)!==0):En(t,n),un=s,zt=r}break;case 30:break;default:En(t,n)}}function gd(t){var e=t.alternate;e!==null&&(t.alternate=null,gd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ou(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Et=null,re=!1;function on(t,e,n){for(n=n.child;n!==null;)vd(t,e,n),n=n.sibling}function vd(t,e,n){if(de&&typeof de.onCommitFiberUnmount=="function")try{de.onCommitFiberUnmount(ui,n)}catch{}switch(n.tag){case 26:zt||Ge(n,e),on(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:zt||Ge(n,e);var a=Et,s=re;jn(n.type)&&(Et=n.stateNode,re=!1),on(t,e,n),Pi(n.stateNode),Et=a,re=s;break;case 5:zt||Ge(n,e);case 6:if(a=Et,s=re,Et=null,on(t,e,n),Et=a,re=s,Et!==null)if(re)try{(Et.nodeType===9?Et.body:Et.nodeName==="HTML"?Et.ownerDocument.body:Et).removeChild(n.stateNode)}catch(r){St(n,e,r)}else try{Et.removeChild(n.stateNode)}catch(r){St(n,e,r)}break;case 18:Et!==null&&(re?(t=Et,sm(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),al(t)):sm(Et,n.stateNode));break;case 4:a=Et,s=re,Et=n.stateNode.containerInfo,re=!0,on(t,e,n),Et=a,re=s;break;case 0:case 11:case 14:case 15:zt||Mn(2,n,e),zt||Mn(4,n,e),on(t,e,n);break;case 1:zt||(Ge(n,e),a=n.stateNode,typeof a.componentWillUnmount=="function"&&fd(n,e,a)),on(t,e,n);break;case 21:on(t,e,n);break;case 22:zt=(a=zt)||n.memoizedState!==null,on(t,e,n),zt=a;break;default:on(t,e,n)}}function bd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{al(t)}catch(n){St(e,e.return,n)}}function sv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new yd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new yd),e;default:throw Error(o(435,t.tag))}}function Co(t,e){var n=sv(t);e.forEach(function(a){var s=yv.bind(null,t,a);n.has(a)||(n.add(a),a.then(s,s))})}function ge(t,e){var n=e.deletions;if(n!==null)for(var a=0;a<n.length;a++){var s=n[a],r=t,f=e,p=f;t:for(;p!==null;){switch(p.tag){case 27:if(jn(p.type)){Et=p.stateNode,re=!1;break t}break;case 5:Et=p.stateNode,re=!1;break t;case 3:case 4:Et=p.stateNode.containerInfo,re=!0;break t}p=p.return}if(Et===null)throw Error(o(160));vd(r,f,s),Et=null,re=!1,r=s.alternate,r!==null&&(r.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)xd(e,t),e=e.sibling}var we=null;function xd(t,e){var n=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ge(e,t),ve(t),a&4&&(Mn(3,t,t.return),Hi(3,t),Mn(5,t,t.return));break;case 1:ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),a&64&&un&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var s=we;if(ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),a&4){var r=n!==null?n.memoizedState:null;if(a=t.memoizedState,n===null)if(a===null)if(t.stateNode===null){t:{a=t.type,n=t.memoizedProps,s=s.ownerDocument||s;e:switch(a){case"title":r=s.getElementsByTagName("title")[0],(!r||r[ci]||r[te]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=s.createElement(a),s.head.insertBefore(r,s.querySelector("head > title"))),Wt(r,a,n),r[te]=t,Xt(r),a=r;break t;case"link":var f=mm("link","href",s).get(a+(n.href||""));if(f){for(var p=0;p<f.length;p++)if(r=f[p],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(p,1);break e}}r=s.createElement(a),Wt(r,a,n),s.head.appendChild(r);break;case"meta":if(f=mm("meta","content",s).get(a+(n.content||""))){for(p=0;p<f.length;p++)if(r=f[p],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(p,1);break e}}r=s.createElement(a),Wt(r,a,n),s.head.appendChild(r);break;default:throw Error(o(468,a))}r[te]=t,Xt(r),a=r}t.stateNode=a}else ym(s,t.type,t.stateNode);else t.stateNode=dm(s,a,t.memoizedProps);else r!==a?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,a===null?ym(s,t.type,t.stateNode):dm(s,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Ro(t,t.memoizedProps,n.memoizedProps)}break;case 27:ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),n!==null&&a&4&&Ro(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),t.flags&32){s=t.stateNode;try{ba(s,"")}catch(N){St(t,t.return,N)}}a&4&&t.stateNode!=null&&(s=t.memoizedProps,Ro(t,s,n!==null?n.memoizedProps:s)),a&1024&&(Vo=!0);break;case 6:if(ge(e,t),ve(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,n=t.stateNode;try{n.nodeValue=a}catch(N){St(t,t.return,N)}}break;case 3:if(Ns=null,s=we,we=Rs(e.containerInfo),ge(e,t),we=s,ve(t),a&4&&n!==null&&n.memoizedState.isDehydrated)try{al(e.containerInfo)}catch(N){St(t,t.return,N)}Vo&&(Vo=!1,Sd(t));break;case 4:a=we,we=Rs(t.stateNode.containerInfo),ge(e,t),ve(t),we=a;break;case 12:ge(e,t),ve(t);break;case 13:ge(e,t),ve(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Bo=He()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Co(t,a)));break;case 22:s=t.memoizedState!==null;var b=n!==null&&n.memoizedState!==null,D=un,C=zt;if(un=D||s,zt=C||b,ge(e,t),zt=C,un=D,ve(t),a&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(n===null||b||un||zt||aa(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){b=n=e;try{if(r=b.stateNode,s)f=r.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{p=b.stateNode;var _=b.memoizedProps.style,O=_!=null&&_.hasOwnProperty("display")?_.display:null;p.style.display=O==null||typeof O=="boolean"?"":(""+O).trim()}}catch(N){St(b,b.return,N)}}}else if(e.tag===6){if(n===null){b=e;try{b.stateNode.nodeValue=s?"":b.memoizedProps}catch(N){St(b,b.return,N)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,Co(t,n))));break;case 19:ge(e,t),ve(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Co(t,a)));break;case 30:break;case 21:break;default:ge(e,t),ve(t)}}function ve(t){var e=t.flags;if(e&2){try{for(var n,a=t.return;a!==null;){if(dd(a)){n=a;break}a=a.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var s=n.stateNode,r=Oo(t);ms(t,r,s);break;case 5:var f=n.stateNode;n.flags&32&&(ba(f,""),n.flags&=-33);var p=Oo(t);ms(t,p,f);break;case 3:case 4:var b=n.stateNode.containerInfo,D=Oo(t);No(t,D,b);break;default:throw Error(o(161))}}catch(C){St(t,t.return,C)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Sd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Sd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function En(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)pd(t,e.alternate,e),e=e.sibling}function aa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Mn(4,e,e.return),aa(e);break;case 1:Ge(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&fd(e,e.return,n),aa(e);break;case 27:Pi(e.stateNode);case 26:case 5:Ge(e,e.return),aa(e);break;case 22:e.memoizedState===null&&aa(e);break;case 30:aa(e);break;default:aa(e)}t=t.sibling}}function Dn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,s=t,r=e,f=r.flags;switch(r.tag){case 0:case 11:case 15:Dn(s,r,n),Hi(4,r);break;case 1:if(Dn(s,r,n),a=r,s=a.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(D){St(a,a.return,D)}if(a=r,s=a.updateQueue,s!==null){var p=a.stateNode;try{var b=s.shared.hiddenCallbacks;if(b!==null)for(s.shared.hiddenCallbacks=null,s=0;s<b.length;s++)Ff(b[s],p)}catch(D){St(a,a.return,D)}}n&&f&64&&cd(r),qi(r,r.return);break;case 27:md(r);case 26:case 5:Dn(s,r,n),n&&a===null&&f&4&&hd(r),qi(r,r.return);break;case 12:Dn(s,r,n);break;case 13:Dn(s,r,n),n&&f&4&&bd(s,r);break;case 22:r.memoizedState===null&&Dn(s,r,n),qi(r,r.return);break;case 30:break;default:Dn(s,r,n)}e=e.sibling}}function jo(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Ei(n))}function zo(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ei(t))}function Xe(t,e,n,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Td(t,e,n,a),e=e.sibling}function Td(t,e,n,a){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Xe(t,e,n,a),s&2048&&Hi(9,e);break;case 1:Xe(t,e,n,a);break;case 3:Xe(t,e,n,a),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ei(t)));break;case 12:if(s&2048){Xe(t,e,n,a),t=e.stateNode;try{var r=e.memoizedProps,f=r.id,p=r.onPostCommit;typeof p=="function"&&p(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(b){St(e,e.return,b)}}else Xe(t,e,n,a);break;case 13:Xe(t,e,n,a);break;case 23:break;case 22:r=e.stateNode,f=e.alternate,e.memoizedState!==null?r._visibility&2?Xe(t,e,n,a):Yi(t,e):r._visibility&2?Xe(t,e,n,a):(r._visibility|=2,La(t,e,n,a,(e.subtreeFlags&10256)!==0)),s&2048&&jo(f,e);break;case 24:Xe(t,e,n,a),s&2048&&zo(e.alternate,e);break;default:Xe(t,e,n,a)}}function La(t,e,n,a,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,f=e,p=n,b=a,D=f.flags;switch(f.tag){case 0:case 11:case 15:La(r,f,p,b,s),Hi(8,f);break;case 23:break;case 22:var C=f.stateNode;f.memoizedState!==null?C._visibility&2?La(r,f,p,b,s):Yi(r,f):(C._visibility|=2,La(r,f,p,b,s)),s&&D&2048&&jo(f.alternate,f);break;case 24:La(r,f,p,b,s),s&&D&2048&&zo(f.alternate,f);break;default:La(r,f,p,b,s)}e=e.sibling}}function Yi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,a=e,s=a.flags;switch(a.tag){case 22:Yi(n,a),s&2048&&jo(a.alternate,a);break;case 24:Yi(n,a),s&2048&&zo(a.alternate,a);break;default:Yi(n,a)}e=e.sibling}}var Gi=8192;function Ha(t){if(t.subtreeFlags&Gi)for(t=t.child;t!==null;)Ad(t),t=t.sibling}function Ad(t){switch(t.tag){case 26:Ha(t),t.flags&Gi&&t.memoizedState!==null&&Qv(we,t.memoizedState,t.memoizedProps);break;case 5:Ha(t);break;case 3:case 4:var e=we;we=Rs(t.stateNode.containerInfo),Ha(t),we=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Gi,Gi=16777216,Ha(t),Gi=e):Ha(t));break;default:Ha(t)}}function Md(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Xi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var a=e[n];Qt=a,Dd(a,t)}Md(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ed(t),t=t.sibling}function Ed(t){switch(t.tag){case 0:case 11:case 15:Xi(t),t.flags&2048&&Mn(9,t,t.return);break;case 3:Xi(t);break;case 12:Xi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ys(t)):Xi(t);break;default:Xi(t)}}function ys(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var a=e[n];Qt=a,Dd(a,t)}Md(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Mn(8,e,e.return),ys(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ys(e));break;default:ys(e)}t=t.sibling}}function Dd(t,e){for(;Qt!==null;){var n=Qt;switch(n.tag){case 0:case 11:case 15:Mn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Ei(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,Qt=a;else t:for(n=t;Qt!==null;){a=Qt;var s=a.sibling,r=a.return;if(gd(a),a===n){Qt=null;break t}if(s!==null){s.return=r,Qt=s;break t}Qt=r}}}var uv={getCacheForType:function(t){var e=ee(qt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},ov=typeof WeakMap=="function"?WeakMap:Map,pt=0,Tt=null,ot=null,ct=0,gt=0,be=null,Rn=!1,qa=!1,_o=!1,rn=0,Vt=0,On=0,ia=0,wo=0,Ne=0,Ya=0,Zi=null,ce=null,Uo=!1,Bo=0,ps=1/0,gs=null,Nn=null,Ft=0,Vn=null,Ga=null,Xa=0,Lo=0,Ho=null,Rd=null,Qi=0,qo=null;function xe(){if((pt&2)!==0&&ct!==0)return ct&-ct;if(j.T!==null){var t=Va;return t!==0?t:ko()}return Gc()}function Od(){Ne===0&&(Ne=(ct&536870912)===0||yt?Lc():536870912);var t=Oe.current;return t!==null&&(t.flags|=32),Ne}function Se(t,e,n){(t===Tt&&(gt===2||gt===9)||t.cancelPendingCommit!==null)&&(Za(t,0),Cn(t,ct,Ne,!1)),ri(t,n),((pt&2)===0||t!==Tt)&&(t===Tt&&((pt&2)===0&&(ia|=n),Vt===4&&Cn(t,ct,Ne,!1)),Ze(t))}function Nd(t,e,n){if((pt&6)!==0)throw Error(o(327));var a=!n&&(e&124)===0&&(e&t.expiredLanes)===0||oi(t,e),s=a?fv(t,e):Xo(t,e,!0),r=a;do{if(s===0){qa&&!a&&Cn(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!rv(n)){s=Xo(t,e,!1),r=!1;continue}if(s===2){if(r=e,t.errorRecoveryDisabledLanes&r)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var p=t;s=Zi;var b=p.current.memoizedState.isDehydrated;if(b&&(Za(p,f).flags|=256),f=Xo(p,f,!1),f!==2){if(_o&&!b){p.errorRecoveryDisabledLanes|=r,ia|=r,s=4;break t}r=ce,ce=s,r!==null&&(ce===null?ce=r:ce.push.apply(ce,r))}s=f}if(r=!1,s!==2)continue}}if(s===1){Za(t,0),Cn(t,e,0,!0);break}t:{switch(a=t,r=s,r){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Cn(a,e,Ne,!Rn);break t;case 2:ce=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(s=Bo+300-He(),10<s)){if(Cn(a,e,Ne,!Rn),Ol(a,0,!0)!==0)break t;a.timeoutHandle=im(Vd.bind(null,a,n,ce,gs,Uo,e,Ne,ia,Ya,Rn,r,2,-0,0),s);break t}Vd(a,n,ce,gs,Uo,e,Ne,ia,Ya,Rn,r,0,-0,0)}}break}while(!0);Ze(t)}function Vd(t,e,n,a,s,r,f,p,b,D,C,_,O,N){if(t.timeoutHandle=-1,_=e.subtreeFlags,(_&8192||(_&16785408)===16785408)&&($i={stylesheets:null,count:0,unsuspend:Zv},Ad(e),_=Kv(),_!==null)){t.cancelPendingCommit=_(Bd.bind(null,t,e,r,n,a,s,f,p,b,C,1,O,N)),Cn(t,r,f,!D);return}Bd(t,e,r,n,a,s,f,p,b)}function rv(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var s=n[a],r=s.getSnapshot;s=s.value;try{if(!ye(r(),s))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Cn(t,e,n,a){e&=~wo,e&=~ia,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var s=e;0<s;){var r=31-me(s),f=1<<r;a[r]=-1,s&=~f}n!==0&&qc(t,n,e)}function vs(){return(pt&6)===0?(Ki(0),!1):!0}function Yo(){if(ot!==null){if(gt===0)var t=ot.return;else t=ot,tn=$n=null,ao(t),Ua=null,Ui=0,t=ot;for(;t!==null;)rd(t.alternate,t),t=t.return;ot=null}}function Za(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Rv(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Yo(),Tt=t,ot=n=We(t.current,null),ct=e,gt=0,be=null,Rn=!1,qa=oi(t,e),_o=!1,Ya=Ne=wo=ia=On=Vt=0,ce=Zi=null,Uo=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var s=31-me(a),r=1<<s;e|=t[s],a&=~r}return rn=e,ql(),n}function Cd(t,e){st=null,j.H=ls,e===Ri||e===Pl?(e=Jf(),gt=3):e===Qf?(e=Jf(),gt=4):gt=e===Ph?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,be=e,ot===null&&(Vt=1,cs(t,Me(e,t.current)))}function jd(){var t=j.H;return j.H=ls,t===null?ls:t}function zd(){var t=j.A;return j.A=uv,t}function Go(){Vt=4,Rn||(ct&4194048)!==ct&&Oe.current!==null||(qa=!0),(On&134217727)===0&&(ia&134217727)===0||Tt===null||Cn(Tt,ct,Ne,!1)}function Xo(t,e,n){var a=pt;pt|=2;var s=jd(),r=zd();(Tt!==t||ct!==e)&&(gs=null,Za(t,e)),e=!1;var f=Vt;t:do try{if(gt!==0&&ot!==null){var p=ot,b=be;switch(gt){case 8:Yo(),f=6;break t;case 3:case 2:case 9:case 6:Oe.current===null&&(e=!0);var D=gt;if(gt=0,be=null,Qa(t,p,b,D),n&&qa){f=0;break t}break;default:D=gt,gt=0,be=null,Qa(t,p,b,D)}}cv(),f=Vt;break}catch(C){Cd(t,C)}while(!0);return e&&t.shellSuspendCounter++,tn=$n=null,pt=a,j.H=s,j.A=r,ot===null&&(Tt=null,ct=0,ql()),f}function cv(){for(;ot!==null;)_d(ot)}function fv(t,e){var n=pt;pt|=2;var a=jd(),s=zd();Tt!==t||ct!==e?(gs=null,ps=He()+500,Za(t,e)):qa=oi(t,e);t:do try{if(gt!==0&&ot!==null){e=ot;var r=be;e:switch(gt){case 1:gt=0,be=null,Qa(t,e,r,1);break;case 2:case 9:if(Kf(r)){gt=0,be=null,wd(e);break}e=function(){gt!==2&&gt!==9||Tt!==t||(gt=7),Ze(t)},r.then(e,e);break t;case 3:gt=7;break t;case 4:gt=5;break t;case 7:Kf(r)?(gt=0,be=null,wd(e)):(gt=0,be=null,Qa(t,e,r,7));break;case 5:var f=null;switch(ot.tag){case 26:f=ot.memoizedState;case 5:case 27:var p=ot;if(!f||pm(f)){gt=0,be=null;var b=p.sibling;if(b!==null)ot=b;else{var D=p.return;D!==null?(ot=D,bs(D)):ot=null}break e}}gt=0,be=null,Qa(t,e,r,5);break;case 6:gt=0,be=null,Qa(t,e,r,6);break;case 8:Yo(),Vt=6;break t;default:throw Error(o(462))}}hv();break}catch(C){Cd(t,C)}while(!0);return tn=$n=null,j.H=a,j.A=s,pt=n,ot!==null?0:(Tt=null,ct=0,ql(),Vt)}function hv(){for(;ot!==null&&!_0();)_d(ot)}function _d(t){var e=ud(t.alternate,t,rn);t.memoizedProps=t.pendingProps,e===null?bs(t):ot=e}function wd(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=ed(n,e,e.pendingProps,e.type,void 0,ct);break;case 11:e=ed(n,e,e.pendingProps,e.type.render,e.ref,ct);break;case 5:ao(e);default:rd(n,e),e=ot=Uf(e,rn),e=ud(n,e,rn)}t.memoizedProps=t.pendingProps,e===null?bs(t):ot=e}function Qa(t,e,n,a){tn=$n=null,ao(e),Ua=null,Ui=0;var s=e.return;try{if(ev(t,s,e,n,ct)){Vt=1,cs(t,Me(n,t.current)),ot=null;return}}catch(r){if(s!==null)throw ot=s,r;Vt=1,cs(t,Me(n,t.current)),ot=null;return}e.flags&32768?(yt||a===1?t=!0:qa||(ct&536870912)!==0?t=!1:(Rn=t=!0,(a===2||a===9||a===3||a===6)&&(a=Oe.current,a!==null&&a.tag===13&&(a.flags|=16384))),Ud(e,t)):bs(e)}function bs(t){var e=t;do{if((e.flags&32768)!==0){Ud(e,Rn);return}t=e.return;var n=av(e.alternate,e,rn);if(n!==null){ot=n;return}if(e=e.sibling,e!==null){ot=e;return}ot=e=t}while(e!==null);Vt===0&&(Vt=5)}function Ud(t,e){do{var n=iv(t.alternate,t);if(n!==null){n.flags&=32767,ot=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){ot=t;return}ot=t=n}while(t!==null);Vt=6,ot=null}function Bd(t,e,n,a,s,r,f,p,b){t.cancelPendingCommit=null;do xs();while(Ft!==0);if((pt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(r=e.lanes|e.childLanes,r|=ju,Z0(t,n,r,f,p,b),t===Tt&&(ot=Tt=null,ct=0),Ga=e,Vn=t,Xa=n,Lo=r,Ho=s,Rd=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,pv(El,function(){return Gd(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=j.T,j.T=null,s=U.p,U.p=2,f=pt,pt|=4;try{lv(t,e,n)}finally{pt=f,U.p=s,j.T=a}}Ft=1,Ld(),Hd(),qd()}}function Ld(){if(Ft===1){Ft=0;var t=Vn,e=Ga,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=j.T,j.T=null;var a=U.p;U.p=2;var s=pt;pt|=4;try{xd(e,t);var r=er,f=Df(t.containerInfo),p=r.focusedElem,b=r.selectionRange;if(f!==p&&p&&p.ownerDocument&&Ef(p.ownerDocument.documentElement,p)){if(b!==null&&Ru(p)){var D=b.start,C=b.end;if(C===void 0&&(C=D),"selectionStart"in p)p.selectionStart=D,p.selectionEnd=Math.min(C,p.value.length);else{var _=p.ownerDocument||document,O=_&&_.defaultView||window;if(O.getSelection){var N=O.getSelection(),I=p.textContent.length,F=Math.min(b.start,I),xt=b.end===void 0?F:Math.min(b.end,I);!N.extend&&F>xt&&(f=xt,xt=F,F=f);var A=Mf(p,F),T=Mf(p,xt);if(A&&T&&(N.rangeCount!==1||N.anchorNode!==A.node||N.anchorOffset!==A.offset||N.focusNode!==T.node||N.focusOffset!==T.offset)){var E=_.createRange();E.setStart(A.node,A.offset),N.removeAllRanges(),F>xt?(N.addRange(E),N.extend(T.node,T.offset)):(E.setEnd(T.node,T.offset),N.addRange(E))}}}}for(_=[],N=p;N=N.parentNode;)N.nodeType===1&&_.push({element:N,left:N.scrollLeft,top:N.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<_.length;p++){var z=_[p];z.element.scrollLeft=z.left,z.element.scrollTop=z.top}}js=!!tr,er=tr=null}finally{pt=s,U.p=a,j.T=n}}t.current=e,Ft=2}}function Hd(){if(Ft===2){Ft=0;var t=Vn,e=Ga,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=j.T,j.T=null;var a=U.p;U.p=2;var s=pt;pt|=4;try{pd(t,e.alternate,e)}finally{pt=s,U.p=a,j.T=n}}Ft=3}}function qd(){if(Ft===4||Ft===3){Ft=0,w0();var t=Vn,e=Ga,n=Xa,a=Rd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ft=5:(Ft=0,Ga=Vn=null,Yd(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(Nn=null),su(n),e=e.stateNode,de&&typeof de.onCommitFiberRoot=="function")try{de.onCommitFiberRoot(ui,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=j.T,s=U.p,U.p=2,j.T=null;try{for(var r=t.onRecoverableError,f=0;f<a.length;f++){var p=a[f];r(p.value,{componentStack:p.stack})}}finally{j.T=e,U.p=s}}(Xa&3)!==0&&xs(),Ze(t),s=t.pendingLanes,(n&4194090)!==0&&(s&42)!==0?t===qo?Qi++:(Qi=0,qo=t):Qi=0,Ki(0)}}function Yd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ei(e)))}function xs(t){return Ld(),Hd(),qd(),Gd()}function Gd(){if(Ft!==5)return!1;var t=Vn,e=Lo;Lo=0;var n=su(Xa),a=j.T,s=U.p;try{U.p=32>n?32:n,j.T=null,n=Ho,Ho=null;var r=Vn,f=Xa;if(Ft=0,Ga=Vn=null,Xa=0,(pt&6)!==0)throw Error(o(331));var p=pt;if(pt|=4,Ed(r.current),Td(r,r.current,f,n),pt=p,Ki(0,!1),de&&typeof de.onPostCommitFiberRoot=="function")try{de.onPostCommitFiberRoot(ui,r)}catch{}return!0}finally{U.p=s,j.T=a,Yd(t,e)}}function Xd(t,e,n){e=Me(n,e),e=vo(t.stateNode,e,2),t=xn(t,e,2),t!==null&&(ri(t,2),Ze(t))}function St(t,e,n){if(t.tag===3)Xd(t,t,n);else for(;e!==null;){if(e.tag===3){Xd(e,t,n);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Nn===null||!Nn.has(a))){t=Me(n,t),n=kh(2),a=xn(e,n,2),a!==null&&(Jh(n,a,e,t),ri(a,2),Ze(a));break}}e=e.return}}function Zo(t,e,n){var a=t.pingCache;if(a===null){a=t.pingCache=new ov;var s=new Set;a.set(e,s)}else s=a.get(e),s===void 0&&(s=new Set,a.set(e,s));s.has(n)||(_o=!0,s.add(n),t=dv.bind(null,t,e,n),e.then(t,t))}function dv(t,e,n){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Tt===t&&(ct&n)===n&&(Vt===4||Vt===3&&(ct&62914560)===ct&&300>He()-Bo?(pt&2)===0&&Za(t,0):wo|=n,Ya===ct&&(Ya=0)),Ze(t)}function Zd(t,e){e===0&&(e=Hc()),t=Da(t,e),t!==null&&(ri(t,e),Ze(t))}function mv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Zd(t,n)}function yv(t,e){var n=0;switch(t.tag){case 13:var a=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),Zd(t,n)}function pv(t,e){return nu(t,e)}var Ss=null,Ka=null,Qo=!1,Ts=!1,Ko=!1,la=0;function Ze(t){t!==Ka&&t.next===null&&(Ka===null?Ss=Ka=t:Ka=Ka.next=t),Ts=!0,Qo||(Qo=!0,vv())}function Ki(t,e){if(!Ko&&Ts){Ko=!0;do for(var n=!1,a=Ss;a!==null;){if(t!==0){var s=a.pendingLanes;if(s===0)var r=0;else{var f=a.suspendedLanes,p=a.pingedLanes;r=(1<<31-me(42|t)+1)-1,r&=s&~(f&~p),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Jd(a,r))}else r=ct,r=Ol(a,a===Tt?r:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(r&3)===0||oi(a,r)||(n=!0,Jd(a,r));a=a.next}while(n);Ko=!1}}function gv(){Qd()}function Qd(){Ts=Qo=!1;var t=0;la!==0&&(Dv()&&(t=la),la=0);for(var e=He(),n=null,a=Ss;a!==null;){var s=a.next,r=Kd(a,e);r===0?(a.next=null,n===null?Ss=s:n.next=s,s===null&&(Ka=n)):(n=a,(t!==0||(r&3)!==0)&&(Ts=!0)),a=s}Ki(t)}function Kd(t,e){for(var n=t.suspendedLanes,a=t.pingedLanes,s=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var f=31-me(r),p=1<<f,b=s[f];b===-1?((p&n)===0||(p&a)!==0)&&(s[f]=X0(p,e)):b<=e&&(t.expiredLanes|=p),r&=~p}if(e=Tt,n=ct,n=Ol(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,n===0||t===e&&(gt===2||gt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&au(a),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||oi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(a!==null&&au(a),su(n)){case 2:case 8:n=Uc;break;case 32:n=El;break;case 268435456:n=Bc;break;default:n=El}return a=kd.bind(null,t),n=nu(n,a),t.callbackPriority=e,t.callbackNode=n,e}return a!==null&&a!==null&&au(a),t.callbackPriority=2,t.callbackNode=null,2}function kd(t,e){if(Ft!==0&&Ft!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(xs()&&t.callbackNode!==n)return null;var a=ct;return a=Ol(t,t===Tt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Nd(t,a,e),Kd(t,He()),t.callbackNode!=null&&t.callbackNode===n?kd.bind(null,t):null)}function Jd(t,e){if(xs())return null;Nd(t,e,!0)}function vv(){Ov(function(){(pt&6)!==0?nu(wc,gv):Qd()})}function ko(){return la===0&&(la=Lc()),la}function Pd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:zl(""+t)}function Fd(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function bv(t,e,n,a,s){if(e==="submit"&&n&&n.stateNode===s){var r=Pd((s[se]||null).action),f=a.submitter;f&&(e=(e=f[se]||null)?Pd(e.formAction):f.getAttribute("formAction"),e!==null&&(r=e,f=null));var p=new Bl("action","action",null,a,s);t.push({event:p,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(la!==0){var b=f?Fd(s,f):new FormData(s);ho(n,{pending:!0,data:b,method:s.method,action:r},null,b)}}else typeof r=="function"&&(p.preventDefault(),b=f?Fd(s,f):new FormData(s),ho(n,{pending:!0,data:b,method:s.method,action:r},r,b))},currentTarget:s}]})}}for(var Jo=0;Jo<Cu.length;Jo++){var Po=Cu[Jo],xv=Po.toLowerCase(),Sv=Po[0].toUpperCase()+Po.slice(1);_e(xv,"on"+Sv)}_e(Nf,"onAnimationEnd"),_e(Vf,"onAnimationIteration"),_e(Cf,"onAnimationStart"),_e("dblclick","onDoubleClick"),_e("focusin","onFocus"),_e("focusout","onBlur"),_e(Lg,"onTransitionRun"),_e(Hg,"onTransitionStart"),_e(qg,"onTransitionCancel"),_e(jf,"onTransitionEnd"),pa("onMouseEnter",["mouseout","mouseover"]),pa("onMouseLeave",["mouseout","mouseover"]),pa("onPointerEnter",["pointerout","pointerover"]),pa("onPointerLeave",["pointerout","pointerover"]),Xn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Xn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Xn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Xn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Xn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Xn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ki="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ki));function Wd(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var a=t[n],s=a.event;a=a.listeners;t:{var r=void 0;if(e)for(var f=a.length-1;0<=f;f--){var p=a[f],b=p.instance,D=p.currentTarget;if(p=p.listener,b!==r&&s.isPropagationStopped())break t;r=p,s.currentTarget=D;try{r(s)}catch(C){rs(C)}s.currentTarget=null,r=b}else for(f=0;f<a.length;f++){if(p=a[f],b=p.instance,D=p.currentTarget,p=p.listener,b!==r&&s.isPropagationStopped())break t;r=p,s.currentTarget=D;try{r(s)}catch(C){rs(C)}s.currentTarget=null,r=b}}}}function rt(t,e){var n=e[uu];n===void 0&&(n=e[uu]=new Set);var a=t+"__bubble";n.has(a)||($d(e,t,2,!1),n.add(a))}function Fo(t,e,n){var a=0;e&&(a|=4),$d(n,t,a,e)}var As="_reactListening"+Math.random().toString(36).slice(2);function Wo(t){if(!t[As]){t[As]=!0,Zc.forEach(function(n){n!=="selectionchange"&&(Tv.has(n)||Fo(n,!1,t),Fo(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[As]||(e[As]=!0,Fo("selectionchange",!1,e))}}function $d(t,e,n,a){switch(Tm(e)){case 2:var s=Pv;break;case 8:s=Fv;break;default:s=fr}n=s.bind(null,e,n,t),s=void 0,!vu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),a?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function $o(t,e,n,a,s){var r=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var f=a.tag;if(f===3||f===4){var p=a.stateNode.containerInfo;if(p===s)break;if(f===4)for(f=a.return;f!==null;){var b=f.tag;if((b===3||b===4)&&f.stateNode.containerInfo===s)return;f=f.return}for(;p!==null;){if(f=da(p),f===null)return;if(b=f.tag,b===5||b===6||b===26||b===27){a=r=f;continue t}p=p.parentNode}}a=a.return}lf(function(){var D=r,C=pu(n),_=[];t:{var O=zf.get(t);if(O!==void 0){var N=Bl,I=t;switch(t){case"keypress":if(wl(n)===0)break t;case"keydown":case"keyup":N=pg;break;case"focusin":I="focus",N=Tu;break;case"focusout":I="blur",N=Tu;break;case"beforeblur":case"afterblur":N=Tu;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=of;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=ig;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=bg;break;case Nf:case Vf:case Cf:N=ug;break;case jf:N=Sg;break;case"scroll":case"scrollend":N=ng;break;case"wheel":N=Ag;break;case"copy":case"cut":case"paste":N=rg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=cf;break;case"toggle":case"beforetoggle":N=Eg}var F=(e&4)!==0,xt=!F&&(t==="scroll"||t==="scrollend"),A=F?O!==null?O+"Capture":null:O;F=[];for(var T=D,E;T!==null;){var z=T;if(E=z.stateNode,z=z.tag,z!==5&&z!==26&&z!==27||E===null||A===null||(z=hi(T,A),z!=null&&F.push(Ji(T,z,E))),xt)break;T=T.return}0<F.length&&(O=new N(O,I,null,n,C),_.push({event:O,listeners:F}))}}if((e&7)===0){t:{if(O=t==="mouseover"||t==="pointerover",N=t==="mouseout"||t==="pointerout",O&&n!==yu&&(I=n.relatedTarget||n.fromElement)&&(da(I)||I[ha]))break t;if((N||O)&&(O=C.window===C?C:(O=C.ownerDocument)?O.defaultView||O.parentWindow:window,N?(I=n.relatedTarget||n.toElement,N=D,I=I?da(I):null,I!==null&&(xt=d(I),F=I.tag,I!==xt||F!==5&&F!==27&&F!==6)&&(I=null)):(N=null,I=D),N!==I)){if(F=of,z="onMouseLeave",A="onMouseEnter",T="mouse",(t==="pointerout"||t==="pointerover")&&(F=cf,z="onPointerLeave",A="onPointerEnter",T="pointer"),xt=N==null?O:fi(N),E=I==null?O:fi(I),O=new F(z,T+"leave",N,n,C),O.target=xt,O.relatedTarget=E,z=null,da(C)===D&&(F=new F(A,T+"enter",I,n,C),F.target=E,F.relatedTarget=xt,z=F),xt=z,N&&I)e:{for(F=N,A=I,T=0,E=F;E;E=ka(E))T++;for(E=0,z=A;z;z=ka(z))E++;for(;0<T-E;)F=ka(F),T--;for(;0<E-T;)A=ka(A),E--;for(;T--;){if(F===A||A!==null&&F===A.alternate)break e;F=ka(F),A=ka(A)}F=null}else F=null;N!==null&&Id(_,O,N,F,!1),I!==null&&xt!==null&&Id(_,xt,I,F,!0)}}t:{if(O=D?fi(D):window,N=O.nodeName&&O.nodeName.toLowerCase(),N==="select"||N==="input"&&O.type==="file")var Q=vf;else if(pf(O))if(bf)Q=wg;else{Q=zg;var ut=jg}else N=O.nodeName,!N||N.toLowerCase()!=="input"||O.type!=="checkbox"&&O.type!=="radio"?D&&mu(D.elementType)&&(Q=vf):Q=_g;if(Q&&(Q=Q(t,D))){gf(_,Q,n,C);break t}ut&&ut(t,O,D),t==="focusout"&&D&&O.type==="number"&&D.memoizedProps.value!=null&&du(O,"number",O.value)}switch(ut=D?fi(D):window,t){case"focusin":(pf(ut)||ut.contentEditable==="true")&&(Aa=ut,Ou=D,xi=null);break;case"focusout":xi=Ou=Aa=null;break;case"mousedown":Nu=!0;break;case"contextmenu":case"mouseup":case"dragend":Nu=!1,Rf(_,n,C);break;case"selectionchange":if(Bg)break;case"keydown":case"keyup":Rf(_,n,C)}var k;if(Mu)t:{switch(t){case"compositionstart":var $="onCompositionStart";break t;case"compositionend":$="onCompositionEnd";break t;case"compositionupdate":$="onCompositionUpdate";break t}$=void 0}else Ta?mf(t,n)&&($="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&($="onCompositionStart");$&&(ff&&n.locale!=="ko"&&(Ta||$!=="onCompositionStart"?$==="onCompositionEnd"&&Ta&&(k=sf()):(pn=C,bu="value"in pn?pn.value:pn.textContent,Ta=!0)),ut=Ms(D,$),0<ut.length&&($=new rf($,t,null,n,C),_.push({event:$,listeners:ut}),k?$.data=k:(k=yf(n),k!==null&&($.data=k)))),(k=Rg?Og(t,n):Ng(t,n))&&($=Ms(D,"onBeforeInput"),0<$.length&&(ut=new rf("onBeforeInput","beforeinput",null,n,C),_.push({event:ut,listeners:$}),ut.data=k)),bv(_,t,D,n,C)}Wd(_,e)})}function Ji(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Ms(t,e){for(var n=e+"Capture",a=[];t!==null;){var s=t,r=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||r===null||(s=hi(t,n),s!=null&&a.unshift(Ji(t,s,r)),s=hi(t,e),s!=null&&a.push(Ji(t,s,r))),t.tag===3)return a;t=t.return}return[]}function ka(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Id(t,e,n,a,s){for(var r=e._reactName,f=[];n!==null&&n!==a;){var p=n,b=p.alternate,D=p.stateNode;if(p=p.tag,b!==null&&b===a)break;p!==5&&p!==26&&p!==27||D===null||(b=D,s?(D=hi(n,r),D!=null&&f.unshift(Ji(n,D,b))):s||(D=hi(n,r),D!=null&&f.push(Ji(n,D,b)))),n=n.return}f.length!==0&&t.push({event:e,listeners:f})}var Av=/\r\n?/g,Mv=/\u0000|\uFFFD/g;function tm(t){return(typeof t=="string"?t:""+t).replace(Av,`
`).replace(Mv,"")}function em(t,e){return e=tm(e),tm(t)===e}function Es(){}function bt(t,e,n,a,s,r){switch(n){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ba(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ba(t,""+a);break;case"className":Vl(t,"class",a);break;case"tabIndex":Vl(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Vl(t,n,a);break;case"style":nf(t,a,r);break;case"data":if(e!=="object"){Vl(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(n);break}a=zl(""+a),t.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&bt(t,e,"name",s.name,s,null),bt(t,e,"formEncType",s.formEncType,s,null),bt(t,e,"formMethod",s.formMethod,s,null),bt(t,e,"formTarget",s.formTarget,s,null)):(bt(t,e,"encType",s.encType,s,null),bt(t,e,"method",s.method,s,null),bt(t,e,"target",s.target,s,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(n);break}a=zl(""+a),t.setAttribute(n,a);break;case"onClick":a!=null&&(t.onclick=Es);break;case"onScroll":a!=null&&rt("scroll",t);break;case"onScrollEnd":a!=null&&rt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}n=zl(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,""+a):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":a===!0?t.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,a):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(n,a):t.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(n):t.setAttribute(n,a);break;case"popover":rt("beforetoggle",t),rt("toggle",t),Nl(t,"popover",a);break;case"xlinkActuate":Pe(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Pe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Pe(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Pe(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Pe(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Pe(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Nl(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=tg.get(n)||n,Nl(t,n,a))}}function Io(t,e,n,a,s,r){switch(n){case"style":nf(t,a,r);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"children":typeof a=="string"?ba(t,a):(typeof a=="number"||typeof a=="bigint")&&ba(t,""+a);break;case"onScroll":a!=null&&rt("scroll",t);break;case"onScrollEnd":a!=null&&rt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Es);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Qc.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),e=n.slice(2,s?n.length-7:void 0),r=t[se]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,s),typeof a=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,a,s);break t}n in t?t[n]=a:a===!0?t.setAttribute(n,""):Nl(t,n,a)}}}function Wt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":rt("error",t),rt("load",t);var a=!1,s=!1,r;for(r in n)if(n.hasOwnProperty(r)){var f=n[r];if(f!=null)switch(r){case"src":a=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:bt(t,e,r,f,n,null)}}s&&bt(t,e,"srcSet",n.srcSet,n,null),a&&bt(t,e,"src",n.src,n,null);return;case"input":rt("invalid",t);var p=r=f=s=null,b=null,D=null;for(a in n)if(n.hasOwnProperty(a)){var C=n[a];if(C!=null)switch(a){case"name":s=C;break;case"type":f=C;break;case"checked":b=C;break;case"defaultChecked":D=C;break;case"value":r=C;break;case"defaultValue":p=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(o(137,e));break;default:bt(t,e,a,C,n,null)}}$c(t,r,p,b,D,f,s,!1),Cl(t);return;case"select":rt("invalid",t),a=f=r=null;for(s in n)if(n.hasOwnProperty(s)&&(p=n[s],p!=null))switch(s){case"value":r=p;break;case"defaultValue":f=p;break;case"multiple":a=p;default:bt(t,e,s,p,n,null)}e=r,n=f,t.multiple=!!a,e!=null?va(t,!!a,e,!1):n!=null&&va(t,!!a,n,!0);return;case"textarea":rt("invalid",t),r=s=a=null;for(f in n)if(n.hasOwnProperty(f)&&(p=n[f],p!=null))switch(f){case"value":a=p;break;case"defaultValue":s=p;break;case"children":r=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(o(91));break;default:bt(t,e,f,p,n,null)}tf(t,a,s,r),Cl(t);return;case"option":for(b in n)if(n.hasOwnProperty(b)&&(a=n[b],a!=null))switch(b){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:bt(t,e,b,a,n,null)}return;case"dialog":rt("beforetoggle",t),rt("toggle",t),rt("cancel",t),rt("close",t);break;case"iframe":case"object":rt("load",t);break;case"video":case"audio":for(a=0;a<ki.length;a++)rt(ki[a],t);break;case"image":rt("error",t),rt("load",t);break;case"details":rt("toggle",t);break;case"embed":case"source":case"link":rt("error",t),rt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(a=n[D],a!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:bt(t,e,D,a,n,null)}return;default:if(mu(e)){for(C in n)n.hasOwnProperty(C)&&(a=n[C],a!==void 0&&Io(t,e,C,a,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(a=n[p],a!=null&&bt(t,e,p,a,n,null))}function Ev(t,e,n,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,r=null,f=null,p=null,b=null,D=null,C=null;for(N in n){var _=n[N];if(n.hasOwnProperty(N)&&_!=null)switch(N){case"checked":break;case"value":break;case"defaultValue":b=_;default:a.hasOwnProperty(N)||bt(t,e,N,null,a,_)}}for(var O in a){var N=a[O];if(_=n[O],a.hasOwnProperty(O)&&(N!=null||_!=null))switch(O){case"type":r=N;break;case"name":s=N;break;case"checked":D=N;break;case"defaultChecked":C=N;break;case"value":f=N;break;case"defaultValue":p=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(o(137,e));break;default:N!==_&&bt(t,e,O,N,a,_)}}hu(t,f,p,b,D,C,r,s);return;case"select":N=f=p=O=null;for(r in n)if(b=n[r],n.hasOwnProperty(r)&&b!=null)switch(r){case"value":break;case"multiple":N=b;default:a.hasOwnProperty(r)||bt(t,e,r,null,a,b)}for(s in a)if(r=a[s],b=n[s],a.hasOwnProperty(s)&&(r!=null||b!=null))switch(s){case"value":O=r;break;case"defaultValue":p=r;break;case"multiple":f=r;default:r!==b&&bt(t,e,s,r,a,b)}e=p,n=f,a=N,O!=null?va(t,!!n,O,!1):!!a!=!!n&&(e!=null?va(t,!!n,e,!0):va(t,!!n,n?[]:"",!1));return;case"textarea":N=O=null;for(p in n)if(s=n[p],n.hasOwnProperty(p)&&s!=null&&!a.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:bt(t,e,p,null,a,s)}for(f in a)if(s=a[f],r=n[f],a.hasOwnProperty(f)&&(s!=null||r!=null))switch(f){case"value":O=s;break;case"defaultValue":N=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(o(91));break;default:s!==r&&bt(t,e,f,s,a,r)}Ic(t,O,N);return;case"option":for(var I in n)if(O=n[I],n.hasOwnProperty(I)&&O!=null&&!a.hasOwnProperty(I))switch(I){case"selected":t.selected=!1;break;default:bt(t,e,I,null,a,O)}for(b in a)if(O=a[b],N=n[b],a.hasOwnProperty(b)&&O!==N&&(O!=null||N!=null))switch(b){case"selected":t.selected=O&&typeof O!="function"&&typeof O!="symbol";break;default:bt(t,e,b,O,a,N)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var F in n)O=n[F],n.hasOwnProperty(F)&&O!=null&&!a.hasOwnProperty(F)&&bt(t,e,F,null,a,O);for(D in a)if(O=a[D],N=n[D],a.hasOwnProperty(D)&&O!==N&&(O!=null||N!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(o(137,e));break;default:bt(t,e,D,O,a,N)}return;default:if(mu(e)){for(var xt in n)O=n[xt],n.hasOwnProperty(xt)&&O!==void 0&&!a.hasOwnProperty(xt)&&Io(t,e,xt,void 0,a,O);for(C in a)O=a[C],N=n[C],!a.hasOwnProperty(C)||O===N||O===void 0&&N===void 0||Io(t,e,C,O,a,N);return}}for(var A in n)O=n[A],n.hasOwnProperty(A)&&O!=null&&!a.hasOwnProperty(A)&&bt(t,e,A,null,a,O);for(_ in a)O=a[_],N=n[_],!a.hasOwnProperty(_)||O===N||O==null&&N==null||bt(t,e,_,O,a,N)}var tr=null,er=null;function Ds(t){return t.nodeType===9?t:t.ownerDocument}function nm(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function am(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function nr(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ar=null;function Dv(){var t=window.event;return t&&t.type==="popstate"?t===ar?!1:(ar=t,!0):(ar=null,!1)}var im=typeof setTimeout=="function"?setTimeout:void 0,Rv=typeof clearTimeout=="function"?clearTimeout:void 0,lm=typeof Promise=="function"?Promise:void 0,Ov=typeof queueMicrotask=="function"?queueMicrotask:typeof lm<"u"?function(t){return lm.resolve(null).then(t).catch(Nv)}:im;function Nv(t){setTimeout(function(){throw t})}function jn(t){return t==="head"}function sm(t,e){var n=e,a=0,s=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<a&&8>a){n=a;var f=t.ownerDocument;if(n&1&&Pi(f.documentElement),n&2&&Pi(f.body),n&4)for(n=f.head,Pi(n),f=n.firstChild;f;){var p=f.nextSibling,b=f.nodeName;f[ci]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=p}}if(s===0){t.removeChild(r),al(e);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:a=n.charCodeAt(0)-48;else a=0;n=r}while(n);al(e)}function ir(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ir(n),ou(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Vv(t,e,n,a){for(;t.nodeType===1;){var s=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[ci])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Ue(t.nextSibling),t===null)break}return null}function Cv(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ue(t.nextSibling),t===null))return null;return t}function lr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function jv(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var a=function(){e(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Ue(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var sr=null;function um(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function om(t,e,n){switch(e=Ds(n),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Pi(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ou(t)}var Ve=new Map,rm=new Set;function Rs(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var cn=U.d;U.d={f:zv,r:_v,D:wv,C:Uv,L:Bv,m:Lv,X:qv,S:Hv,M:Yv};function zv(){var t=cn.f(),e=vs();return t||e}function _v(t){var e=ma(t);e!==null&&e.tag===5&&e.type==="form"?Nh(e):cn.r(t)}var Ja=typeof document>"u"?null:document;function cm(t,e,n){var a=Ja;if(a&&typeof e=="string"&&e){var s=Ae(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),rm.has(s)||(rm.add(s),t={rel:t,crossOrigin:n,href:e},a.querySelector(s)===null&&(e=a.createElement("link"),Wt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function wv(t){cn.D(t),cm("dns-prefetch",t,null)}function Uv(t,e){cn.C(t,e),cm("preconnect",t,e)}function Bv(t,e,n){cn.L(t,e,n);var a=Ja;if(a&&t&&e){var s='link[rel="preload"][as="'+Ae(e)+'"]';e==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+Ae(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+Ae(n.imageSizes)+'"]')):s+='[href="'+Ae(t)+'"]';var r=s;switch(e){case"style":r=Pa(t);break;case"script":r=Fa(t)}Ve.has(r)||(t=v({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Ve.set(r,t),a.querySelector(s)!==null||e==="style"&&a.querySelector(Fi(r))||e==="script"&&a.querySelector(Wi(r))||(e=a.createElement("link"),Wt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function Lv(t,e){cn.m(t,e);var n=Ja;if(n&&t){var a=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+Ae(a)+'"][href="'+Ae(t)+'"]',r=s;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Fa(t)}if(!Ve.has(r)&&(t=v({rel:"modulepreload",href:t},e),Ve.set(r,t),n.querySelector(s)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Wi(r)))return}a=n.createElement("link"),Wt(a,"link",t),Xt(a),n.head.appendChild(a)}}}function Hv(t,e,n){cn.S(t,e,n);var a=Ja;if(a&&t){var s=ya(a).hoistableStyles,r=Pa(t);e=e||"default";var f=s.get(r);if(!f){var p={loading:0,preload:null};if(f=a.querySelector(Fi(r)))p.loading=5;else{t=v({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Ve.get(r))&&ur(t,n);var b=f=a.createElement("link");Xt(b),Wt(b,"link",t),b._p=new Promise(function(D,C){b.onload=D,b.onerror=C}),b.addEventListener("load",function(){p.loading|=1}),b.addEventListener("error",function(){p.loading|=2}),p.loading|=4,Os(f,e,a)}f={type:"stylesheet",instance:f,count:1,state:p},s.set(r,f)}}}function qv(t,e){cn.X(t,e);var n=Ja;if(n&&t){var a=ya(n).hoistableScripts,s=Fa(t),r=a.get(s);r||(r=n.querySelector(Wi(s)),r||(t=v({src:t,async:!0},e),(e=Ve.get(s))&&or(t,e),r=n.createElement("script"),Xt(r),Wt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(s,r))}}function Yv(t,e){cn.M(t,e);var n=Ja;if(n&&t){var a=ya(n).hoistableScripts,s=Fa(t),r=a.get(s);r||(r=n.querySelector(Wi(s)),r||(t=v({src:t,async:!0,type:"module"},e),(e=Ve.get(s))&&or(t,e),r=n.createElement("script"),Xt(r),Wt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(s,r))}}function fm(t,e,n,a){var s=(s=nt.current)?Rs(s):null;if(!s)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Pa(n.href),n=ya(s).hoistableStyles,a=n.get(e),a||(a={type:"style",instance:null,count:0,state:null},n.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Pa(n.href);var r=ya(s).hoistableStyles,f=r.get(t);if(f||(s=s.ownerDocument||s,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,f),(r=s.querySelector(Fi(t)))&&!r._p&&(f.instance=r,f.state.loading=5),Ve.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ve.set(t,n),r||Gv(s,t,n,f.state))),e&&a===null)throw Error(o(528,""));return f}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Fa(n),n=ya(s).hoistableScripts,a=n.get(e),a||(a={type:"script",instance:null,count:0,state:null},n.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function Pa(t){return'href="'+Ae(t)+'"'}function Fi(t){return'link[rel="stylesheet"]['+t+"]"}function hm(t){return v({},t,{"data-precedence":t.precedence,precedence:null})}function Gv(t,e,n,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Wt(e,"link",n),Xt(e),t.head.appendChild(e))}function Fa(t){return'[src="'+Ae(t)+'"]'}function Wi(t){return"script[async]"+t}function dm(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+Ae(n.href)+'"]');if(a)return e.instance=a,Xt(a),a;var s=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Xt(a),Wt(a,"style",s),Os(a,n.precedence,t),e.instance=a;case"stylesheet":s=Pa(n.href);var r=t.querySelector(Fi(s));if(r)return e.state.loading|=4,e.instance=r,Xt(r),r;a=hm(n),(s=Ve.get(s))&&ur(a,s),r=(t.ownerDocument||t).createElement("link"),Xt(r);var f=r;return f._p=new Promise(function(p,b){f.onload=p,f.onerror=b}),Wt(r,"link",a),e.state.loading|=4,Os(r,n.precedence,t),e.instance=r;case"script":return r=Fa(n.src),(s=t.querySelector(Wi(r)))?(e.instance=s,Xt(s),s):(a=n,(s=Ve.get(r))&&(a=v({},n),or(a,s)),t=t.ownerDocument||t,s=t.createElement("script"),Xt(s),Wt(s,"link",a),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Os(a,n.precedence,t));return e.instance}function Os(t,e,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=a.length?a[a.length-1]:null,r=s,f=0;f<a.length;f++){var p=a[f];if(p.dataset.precedence===e)r=p;else if(r!==s)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function ur(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function or(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ns=null;function mm(t,e,n){if(Ns===null){var a=new Map,s=Ns=new Map;s.set(n,a)}else s=Ns,a=s.get(n),a||(a=new Map,s.set(n,a));if(a.has(t))return a;for(a.set(t,null),n=n.getElementsByTagName(t),s=0;s<n.length;s++){var r=n[s];if(!(r[ci]||r[te]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var f=r.getAttribute(e)||"";f=t+f;var p=a.get(f);p?p.push(r):a.set(f,[r])}}return a}function ym(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Xv(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function pm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var $i=null;function Zv(){}function Qv(t,e,n){if($i===null)throw Error(o(475));var a=$i;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=Pa(n.href),r=t.querySelector(Fi(s));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Vs.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=r,Xt(r);return}r=t.ownerDocument||t,n=hm(n),(s=Ve.get(s))&&ur(n,s),r=r.createElement("link"),Xt(r);var f=r;f._p=new Promise(function(p,b){f.onload=p,f.onerror=b}),Wt(r,"link",n),e.instance=r}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Vs.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function Kv(){if($i===null)throw Error(o(475));var t=$i;return t.stylesheets&&t.count===0&&rr(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&rr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Vs(){if(this.count--,this.count===0){if(this.stylesheets)rr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Cs=null;function rr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Cs=new Map,e.forEach(kv,t),Cs=null,Vs.call(t))}function kv(t,e){if(!(e.state.loading&4)){var n=Cs.get(t);if(n)var a=n.get(null);else{n=new Map,Cs.set(t,n);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<s.length;r++){var f=s[r];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),a=f)}a&&n.set(null,a)}s=e.instance,f=s.getAttribute("data-precedence"),r=n.get(f)||a,r===a&&n.set(null,s),n.set(f,s),this.count++,a=Vs.bind(this),s.addEventListener("load",a),s.addEventListener("error",a),r?r.parentNode.insertBefore(s,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var Ii={$$typeof:H,Provider:null,Consumer:null,_currentValue:K,_currentValue2:K,_threadCount:0};function Jv(t,e,n,a,s,r,f,p){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=iu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=iu(0),this.hiddenUpdates=iu(null),this.identifierPrefix=a,this.onUncaughtError=s,this.onCaughtError=r,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function gm(t,e,n,a,s,r,f,p,b,D,C,_){return t=new Jv(t,e,n,f,p,b,D,_),e=1,r===!0&&(e|=24),r=pe(3,null,null,e),t.current=r,r.stateNode=t,e=Xu(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:a,isDehydrated:n,cache:e},ku(r),t}function vm(t){return t?(t=Ra,t):Ra}function bm(t,e,n,a,s,r){s=vm(s),a.context===null?a.context=s:a.pendingContext=s,a=bn(e),a.payload={element:n},r=r===void 0?null:r,r!==null&&(a.callback=r),n=xn(t,a,e),n!==null&&(Se(n,t,e),Ni(n,t,e))}function xm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function cr(t,e){xm(t,e),(t=t.alternate)&&xm(t,e)}function Sm(t){if(t.tag===13){var e=Da(t,67108864);e!==null&&Se(e,t,67108864),cr(t,67108864)}}var js=!0;function Pv(t,e,n,a){var s=j.T;j.T=null;var r=U.p;try{U.p=2,fr(t,e,n,a)}finally{U.p=r,j.T=s}}function Fv(t,e,n,a){var s=j.T;j.T=null;var r=U.p;try{U.p=8,fr(t,e,n,a)}finally{U.p=r,j.T=s}}function fr(t,e,n,a){if(js){var s=hr(a);if(s===null)$o(t,e,a,zs,n),Am(t,a);else if($v(s,t,e,n,a))a.stopPropagation();else if(Am(t,a),e&4&&-1<Wv.indexOf(t)){for(;s!==null;){var r=ma(s);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var f=Gn(r.pendingLanes);if(f!==0){var p=r;for(p.pendingLanes|=2,p.entangledLanes|=2;f;){var b=1<<31-me(f);p.entanglements[1]|=b,f&=~b}Ze(r),(pt&6)===0&&(ps=He()+500,Ki(0))}}break;case 13:p=Da(r,2),p!==null&&Se(p,r,2),vs(),cr(r,2)}if(r=hr(a),r===null&&$o(t,e,a,zs,n),r===s)break;s=r}s!==null&&a.stopPropagation()}else $o(t,e,a,null,n)}}function hr(t){return t=pu(t),dr(t)}var zs=null;function dr(t){if(zs=null,t=da(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=h(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return zs=t,null}function Tm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(U0()){case wc:return 2;case Uc:return 8;case El:case B0:return 32;case Bc:return 268435456;default:return 32}default:return 32}}var mr=!1,zn=null,_n=null,wn=null,tl=new Map,el=new Map,Un=[],Wv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Am(t,e){switch(t){case"focusin":case"focusout":zn=null;break;case"dragenter":case"dragleave":_n=null;break;case"mouseover":case"mouseout":wn=null;break;case"pointerover":case"pointerout":tl.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":el.delete(e.pointerId)}}function nl(t,e,n,a,s,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:a,nativeEvent:r,targetContainers:[s]},e!==null&&(e=ma(e),e!==null&&Sm(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function $v(t,e,n,a,s){switch(e){case"focusin":return zn=nl(zn,t,e,n,a,s),!0;case"dragenter":return _n=nl(_n,t,e,n,a,s),!0;case"mouseover":return wn=nl(wn,t,e,n,a,s),!0;case"pointerover":var r=s.pointerId;return tl.set(r,nl(tl.get(r)||null,t,e,n,a,s)),!0;case"gotpointercapture":return r=s.pointerId,el.set(r,nl(el.get(r)||null,t,e,n,a,s)),!0}return!1}function Mm(t){var e=da(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=h(n),e!==null){t.blockedOn=e,Q0(t.priority,function(){if(n.tag===13){var a=xe();a=lu(a);var s=Da(n,a);s!==null&&Se(s,n,a),cr(n,a)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function _s(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=hr(t.nativeEvent);if(n===null){n=t.nativeEvent;var a=new n.constructor(n.type,n);yu=a,n.target.dispatchEvent(a),yu=null}else return e=ma(n),e!==null&&Sm(e),t.blockedOn=n,!1;e.shift()}return!0}function Em(t,e,n){_s(t)&&n.delete(e)}function Iv(){mr=!1,zn!==null&&_s(zn)&&(zn=null),_n!==null&&_s(_n)&&(_n=null),wn!==null&&_s(wn)&&(wn=null),tl.forEach(Em),el.forEach(Em)}function ws(t,e){t.blockedOn===e&&(t.blockedOn=null,mr||(mr=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Iv)))}var Us=null;function Dm(t){Us!==t&&(Us=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Us===t&&(Us=null);for(var e=0;e<t.length;e+=3){var n=t[e],a=t[e+1],s=t[e+2];if(typeof a!="function"){if(dr(a||n)===null)continue;break}var r=ma(n);r!==null&&(t.splice(e,3),e-=3,ho(r,{pending:!0,data:s,method:n.method,action:a},a,s))}}))}function al(t){function e(b){return ws(b,t)}zn!==null&&ws(zn,t),_n!==null&&ws(_n,t),wn!==null&&ws(wn,t),tl.forEach(e),el.forEach(e);for(var n=0;n<Un.length;n++){var a=Un[n];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Un.length&&(n=Un[0],n.blockedOn===null);)Mm(n),n.blockedOn===null&&Un.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var s=n[a],r=n[a+1],f=s[se]||null;if(typeof r=="function")f||Dm(n);else if(f){var p=null;if(r&&r.hasAttribute("formAction")){if(s=r,f=r[se]||null)p=f.formAction;else if(dr(s)!==null)continue}else p=f.action;typeof p=="function"?n[a+1]=p:(n.splice(a,3),a-=3),Dm(n)}}}function yr(t){this._internalRoot=t}Bs.prototype.render=yr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var n=e.current,a=xe();bm(n,a,t,e,null,null)},Bs.prototype.unmount=yr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;bm(t.current,2,null,t,null,null),vs(),e[ha]=null}};function Bs(t){this._internalRoot=t}Bs.prototype.unstable_scheduleHydration=function(t){if(t){var e=Gc();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Un.length&&e!==0&&e<Un[n].priority;n++);Un.splice(n,0,t),n===0&&Mm(t)}};var Rm=l.version;if(Rm!=="19.1.0")throw Error(o(527,Rm,"19.1.0"));U.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=g(e),t=t!==null?y(t):null,t=t===null?null:t.stateNode,t};var t1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ls=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ls.isDisabled&&Ls.supportsFiber)try{ui=Ls.inject(t1),de=Ls}catch{}}return ll.createRoot=function(t,e){if(!c(t))throw Error(o(299));var n=!1,a="",s=Xh,r=Zh,f=Qh,p=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(p=e.unstable_transitionCallbacks)),e=gm(t,1,!1,null,null,n,a,s,r,f,p,null),t[ha]=e.current,Wo(t),new yr(e)},ll.hydrateRoot=function(t,e,n){if(!c(t))throw Error(o(299));var a=!1,s="",r=Xh,f=Zh,p=Qh,b=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(b=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),e=gm(t,1,!0,e,n??null,a,s,r,f,p,b,D),e.context=vm(null),n=e.current,a=xe(),a=lu(a),s=bn(a),s.callback=null,xn(n,s,a),n=a,e.current.lanes=n,ri(e,n),Ze(e),t[ha]=e.current,Wo(t),new Bs(e)},ll.version="19.1.0",ll}var Bm;function f1(){if(Bm)return vr.exports;Bm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),vr.exports=c1(),vr.exports}var h1=f1();const d1=ep(h1),np=et.createContext({});function m1(i){const l=et.useRef(null);return l.current===null&&(l.current=i()),l.current}const ac=typeof window<"u",y1=ac?et.useLayoutEffect:et.useEffect,ic=et.createContext(null);function lc(i,l){i.indexOf(l)===-1&&i.push(l)}function sc(i,l){const u=i.indexOf(l);u>-1&&i.splice(u,1)}const fn=(i,l,u)=>u>l?l:u<i?i:u;let uc=()=>{};const hn={},ap=i=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(i);function ip(i){return typeof i=="object"&&i!==null}const lp=i=>/^0[^.\s]+$/u.test(i);function oc(i){let l;return()=>(l===void 0&&(l=i()),l)}const ze=i=>i,p1=(i,l)=>u=>l(i(u)),xl=(...i)=>i.reduce(p1),dl=(i,l,u)=>{const o=l-i;return o===0?1:(u-i)/o};class rc{constructor(){this.subscriptions=[]}add(l){return lc(this.subscriptions,l),()=>sc(this.subscriptions,l)}notify(l,u,o){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](l,u,o);else for(let d=0;d<c;d++){const h=this.subscriptions[d];h&&h(l,u,o)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Qe=i=>i*1e3,Ke=i=>i/1e3;function sp(i,l){return l?i*(1e3/l):0}const up=(i,l,u)=>(((1-3*u+3*l)*i+(3*u-6*l))*i+3*l)*i,g1=1e-7,v1=12;function b1(i,l,u,o,c){let d,h,m=0;do h=l+(u-l)/2,d=up(h,o,c)-i,d>0?u=h:l=h;while(Math.abs(d)>g1&&++m<v1);return h}function Sl(i,l,u,o){if(i===l&&u===o)return ze;const c=d=>b1(d,0,1,i,u);return d=>d===0||d===1?d:up(c(d),l,o)}const op=i=>l=>l<=.5?i(2*l)/2:(2-i(2*(1-l)))/2,rp=i=>l=>1-i(1-l),cp=Sl(.33,1.53,.69,.99),cc=rp(cp),fp=op(cc),hp=i=>(i*=2)<1?.5*cc(i):.5*(2-Math.pow(2,-10*(i-1))),fc=i=>1-Math.sin(Math.acos(i)),dp=rp(fc),mp=op(fc),x1=Sl(.42,0,1,1),S1=Sl(0,0,.58,1),yp=Sl(.42,0,.58,1),T1=i=>Array.isArray(i)&&typeof i[0]!="number",pp=i=>Array.isArray(i)&&typeof i[0]=="number",A1={linear:ze,easeIn:x1,easeInOut:yp,easeOut:S1,circIn:fc,circInOut:mp,circOut:dp,backIn:cc,backInOut:fp,backOut:cp,anticipate:hp},M1=i=>typeof i=="string",Lm=i=>{if(pp(i)){uc(i.length===4);const[l,u,o,c]=i;return Sl(l,u,o,c)}else if(M1(i))return A1[i];return i},Hs=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Hm={value:null};function E1(i,l){let u=new Set,o=new Set,c=!1,d=!1;const h=new WeakSet;let m={delta:0,timestamp:0,isProcessing:!1},g=0;function y(x){h.has(x)&&(v.schedule(x),i()),g++,x(m)}const v={schedule:(x,M=!1,V=!1)=>{const L=V&&c?u:o;return M&&h.add(x),L.has(x)||L.add(x),x},cancel:x=>{o.delete(x),h.delete(x)},process:x=>{if(m=x,c){d=!0;return}c=!0,[u,o]=[o,u],u.forEach(y),l&&Hm.value&&Hm.value.frameloop[l].push(g),g=0,u.clear(),c=!1,d&&(d=!1,v.process(x))}};return v}const D1=40;function gp(i,l){let u=!1,o=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>u=!0,h=Hs.reduce((H,it)=>(H[it]=E1(d,l?it:void 0),H),{}),{setup:m,read:g,resolveKeyframes:y,preUpdate:v,update:x,preRender:M,render:V,postRender:B}=h,L=()=>{const H=hn.useManualTiming?c.timestamp:performance.now();u=!1,hn.useManualTiming||(c.delta=o?1e3/60:Math.max(Math.min(H-c.timestamp,D1),1)),c.timestamp=H,c.isProcessing=!0,m.process(c),g.process(c),y.process(c),v.process(c),x.process(c),M.process(c),V.process(c),B.process(c),c.isProcessing=!1,u&&l&&(o=!1,i(L))},Y=()=>{u=!0,o=!0,c.isProcessing||i(L)};return{schedule:Hs.reduce((H,it)=>{const q=h[it];return H[it]=(lt,ft=!1,W=!1)=>(u||Y(),q.schedule(lt,ft,W)),H},{}),cancel:H=>{for(let it=0;it<Hs.length;it++)h[Hs[it]].cancel(H)},state:c,steps:h}}const{schedule:Ot,cancel:Hn,state:$t,steps:Tr}=gp(typeof requestAnimationFrame<"u"?requestAnimationFrame:ze,!0);let Gs;function R1(){Gs=void 0}const fe={now:()=>(Gs===void 0&&fe.set($t.isProcessing||hn.useManualTiming?$t.timestamp:performance.now()),Gs),set:i=>{Gs=i,queueMicrotask(R1)}},vp=i=>l=>typeof l=="string"&&l.startsWith(i),hc=vp("--"),O1=vp("var(--"),dc=i=>O1(i)?N1.test(i.split("/*")[0].trim()):!1,N1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ii={test:i=>typeof i=="number",parse:parseFloat,transform:i=>i},ml={...ii,transform:i=>fn(0,1,i)},qs={...ii,default:1},ol=i=>Math.round(i*1e5)/1e5,mc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function V1(i){return i==null}const C1=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,yc=(i,l)=>u=>!!(typeof u=="string"&&C1.test(u)&&u.startsWith(i)||l&&!V1(u)&&Object.prototype.hasOwnProperty.call(u,l)),bp=(i,l,u)=>o=>{if(typeof o!="string")return o;const[c,d,h,m]=o.match(mc);return{[i]:parseFloat(c),[l]:parseFloat(d),[u]:parseFloat(h),alpha:m!==void 0?parseFloat(m):1}},j1=i=>fn(0,255,i),Ar={...ii,transform:i=>Math.round(j1(i))},oa={test:yc("rgb","red"),parse:bp("red","green","blue"),transform:({red:i,green:l,blue:u,alpha:o=1})=>"rgba("+Ar.transform(i)+", "+Ar.transform(l)+", "+Ar.transform(u)+", "+ol(ml.transform(o))+")"};function z1(i){let l="",u="",o="",c="";return i.length>5?(l=i.substring(1,3),u=i.substring(3,5),o=i.substring(5,7),c=i.substring(7,9)):(l=i.substring(1,2),u=i.substring(2,3),o=i.substring(3,4),c=i.substring(4,5),l+=l,u+=u,o+=o,c+=c),{red:parseInt(l,16),green:parseInt(u,16),blue:parseInt(o,16),alpha:c?parseInt(c,16)/255:1}}const Ur={test:yc("#"),parse:z1,transform:oa.transform},Tl=i=>({test:l=>typeof l=="string"&&l.endsWith(i)&&l.split(" ").length===1,parse:parseFloat,transform:l=>`${l}${i}`}),Ln=Tl("deg"),ke=Tl("%"),tt=Tl("px"),_1=Tl("vh"),w1=Tl("vw"),qm={...ke,parse:i=>ke.parse(i)/100,transform:i=>ke.transform(i*100)},Wa={test:yc("hsl","hue"),parse:bp("hue","saturation","lightness"),transform:({hue:i,saturation:l,lightness:u,alpha:o=1})=>"hsla("+Math.round(i)+", "+ke.transform(ol(l))+", "+ke.transform(ol(u))+", "+ol(ml.transform(o))+")"},Bt={test:i=>oa.test(i)||Ur.test(i)||Wa.test(i),parse:i=>oa.test(i)?oa.parse(i):Wa.test(i)?Wa.parse(i):Ur.parse(i),transform:i=>typeof i=="string"?i:i.hasOwnProperty("red")?oa.transform(i):Wa.transform(i),getAnimatableNone:i=>{const l=Bt.parse(i);return l.alpha=0,Bt.transform(l)}},U1=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function B1(i){var l,u;return isNaN(i)&&typeof i=="string"&&(((l=i.match(mc))==null?void 0:l.length)||0)+(((u=i.match(U1))==null?void 0:u.length)||0)>0}const xp="number",Sp="color",L1="var",H1="var(",Ym="${}",q1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function yl(i){const l=i.toString(),u=[],o={color:[],number:[],var:[]},c=[];let d=0;const m=l.replace(q1,g=>(Bt.test(g)?(o.color.push(d),c.push(Sp),u.push(Bt.parse(g))):g.startsWith(H1)?(o.var.push(d),c.push(L1),u.push(g)):(o.number.push(d),c.push(xp),u.push(parseFloat(g))),++d,Ym)).split(Ym);return{values:u,split:m,indexes:o,types:c}}function Tp(i){return yl(i).values}function Ap(i){const{split:l,types:u}=yl(i),o=l.length;return c=>{let d="";for(let h=0;h<o;h++)if(d+=l[h],c[h]!==void 0){const m=u[h];m===xp?d+=ol(c[h]):m===Sp?d+=Bt.transform(c[h]):d+=c[h]}return d}}const Y1=i=>typeof i=="number"?0:Bt.test(i)?Bt.getAnimatableNone(i):i;function G1(i){const l=Tp(i);return Ap(i)(l.map(Y1))}const qn={test:B1,parse:Tp,createTransformer:Ap,getAnimatableNone:G1};function Mr(i,l,u){return u<0&&(u+=1),u>1&&(u-=1),u<1/6?i+(l-i)*6*u:u<1/2?l:u<2/3?i+(l-i)*(2/3-u)*6:i}function X1({hue:i,saturation:l,lightness:u,alpha:o}){i/=360,l/=100,u/=100;let c=0,d=0,h=0;if(!l)c=d=h=u;else{const m=u<.5?u*(1+l):u+l-u*l,g=2*u-m;c=Mr(g,m,i+1/3),d=Mr(g,m,i),h=Mr(g,m,i-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(h*255),alpha:o}}function Ks(i,l){return u=>u>0?l:i}const Rt=(i,l,u)=>i+(l-i)*u,Er=(i,l,u)=>{const o=i*i,c=u*(l*l-o)+o;return c<0?0:Math.sqrt(c)},Z1=[Ur,oa,Wa],Q1=i=>Z1.find(l=>l.test(i));function Gm(i){const l=Q1(i);if(!l)return!1;let u=l.parse(i);return l===Wa&&(u=X1(u)),u}const Xm=(i,l)=>{const u=Gm(i),o=Gm(l);if(!u||!o)return Ks(i,l);const c={...u};return d=>(c.red=Er(u.red,o.red,d),c.green=Er(u.green,o.green,d),c.blue=Er(u.blue,o.blue,d),c.alpha=Rt(u.alpha,o.alpha,d),oa.transform(c))},Br=new Set(["none","hidden"]);function K1(i,l){return Br.has(i)?u=>u<=0?i:l:u=>u>=1?l:i}function k1(i,l){return u=>Rt(i,l,u)}function pc(i){return typeof i=="number"?k1:typeof i=="string"?dc(i)?Ks:Bt.test(i)?Xm:F1:Array.isArray(i)?Mp:typeof i=="object"?Bt.test(i)?Xm:J1:Ks}function Mp(i,l){const u=[...i],o=u.length,c=i.map((d,h)=>pc(d)(d,l[h]));return d=>{for(let h=0;h<o;h++)u[h]=c[h](d);return u}}function J1(i,l){const u={...i,...l},o={};for(const c in u)i[c]!==void 0&&l[c]!==void 0&&(o[c]=pc(i[c])(i[c],l[c]));return c=>{for(const d in o)u[d]=o[d](c);return u}}function P1(i,l){const u=[],o={color:0,var:0,number:0};for(let c=0;c<l.values.length;c++){const d=l.types[c],h=i.indexes[d][o[d]],m=i.values[h]??0;u[c]=m,o[d]++}return u}const F1=(i,l)=>{const u=qn.createTransformer(l),o=yl(i),c=yl(l);return o.indexes.var.length===c.indexes.var.length&&o.indexes.color.length===c.indexes.color.length&&o.indexes.number.length>=c.indexes.number.length?Br.has(i)&&!c.values.length||Br.has(l)&&!o.values.length?K1(i,l):xl(Mp(P1(o,c),c.values),u):Ks(i,l)};function Ep(i,l,u){return typeof i=="number"&&typeof l=="number"&&typeof u=="number"?Rt(i,l,u):pc(i)(i,l)}const W1=i=>{const l=({timestamp:u})=>i(u);return{start:(u=!0)=>Ot.update(l,u),stop:()=>Hn(l),now:()=>$t.isProcessing?$t.timestamp:fe.now()}},Dp=(i,l,u=10)=>{let o="";const c=Math.max(Math.round(l/u),2);for(let d=0;d<c;d++)o+=Math.round(i(d/(c-1))*1e4)/1e4+", ";return`linear(${o.substring(0,o.length-2)})`},ks=2e4;function gc(i){let l=0;const u=50;let o=i.next(l);for(;!o.done&&l<ks;)l+=u,o=i.next(l);return l>=ks?1/0:l}function $1(i,l=100,u){const o=u({...i,keyframes:[0,l]}),c=Math.min(gc(o),ks);return{type:"keyframes",ease:d=>o.next(c*d).value/l,duration:Ke(c)}}const I1=5;function Rp(i,l,u){const o=Math.max(l-I1,0);return sp(u-i(o),l-o)}const Ct={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Dr=.001;function tb({duration:i=Ct.duration,bounce:l=Ct.bounce,velocity:u=Ct.velocity,mass:o=Ct.mass}){let c,d,h=1-l;h=fn(Ct.minDamping,Ct.maxDamping,h),i=fn(Ct.minDuration,Ct.maxDuration,Ke(i)),h<1?(c=y=>{const v=y*h,x=v*i,M=v-u,V=Lr(y,h),B=Math.exp(-x);return Dr-M/V*B},d=y=>{const x=y*h*i,M=x*u+u,V=Math.pow(h,2)*Math.pow(y,2)*i,B=Math.exp(-x),L=Lr(Math.pow(y,2),h);return(-c(y)+Dr>0?-1:1)*((M-V)*B)/L}):(c=y=>{const v=Math.exp(-y*i),x=(y-u)*i+1;return-Dr+v*x},d=y=>{const v=Math.exp(-y*i),x=(u-y)*(i*i);return v*x});const m=5/i,g=nb(c,d,m);if(i=Qe(i),isNaN(g))return{stiffness:Ct.stiffness,damping:Ct.damping,duration:i};{const y=Math.pow(g,2)*o;return{stiffness:y,damping:h*2*Math.sqrt(o*y),duration:i}}}const eb=12;function nb(i,l,u){let o=u;for(let c=1;c<eb;c++)o=o-i(o)/l(o);return o}function Lr(i,l){return i*Math.sqrt(1-l*l)}const ab=["duration","bounce"],ib=["stiffness","damping","mass"];function Zm(i,l){return l.some(u=>i[u]!==void 0)}function lb(i){let l={velocity:Ct.velocity,stiffness:Ct.stiffness,damping:Ct.damping,mass:Ct.mass,isResolvedFromDuration:!1,...i};if(!Zm(i,ib)&&Zm(i,ab))if(i.visualDuration){const u=i.visualDuration,o=2*Math.PI/(u*1.2),c=o*o,d=2*fn(.05,1,1-(i.bounce||0))*Math.sqrt(c);l={...l,mass:Ct.mass,stiffness:c,damping:d}}else{const u=tb(i);l={...l,...u,mass:Ct.mass},l.isResolvedFromDuration=!0}return l}function Js(i=Ct.visualDuration,l=Ct.bounce){const u=typeof i!="object"?{visualDuration:i,keyframes:[0,1],bounce:l}:i;let{restSpeed:o,restDelta:c}=u;const d=u.keyframes[0],h=u.keyframes[u.keyframes.length-1],m={done:!1,value:d},{stiffness:g,damping:y,mass:v,duration:x,velocity:M,isResolvedFromDuration:V}=lb({...u,velocity:-Ke(u.velocity||0)}),B=M||0,L=y/(2*Math.sqrt(g*v)),Y=h-d,G=Ke(Math.sqrt(g/v)),J=Math.abs(Y)<5;o||(o=J?Ct.restSpeed.granular:Ct.restSpeed.default),c||(c=J?Ct.restDelta.granular:Ct.restDelta.default);let H;if(L<1){const q=Lr(G,L);H=lt=>{const ft=Math.exp(-L*G*lt);return h-ft*((B+L*G*Y)/q*Math.sin(q*lt)+Y*Math.cos(q*lt))}}else if(L===1)H=q=>h-Math.exp(-G*q)*(Y+(B+G*Y)*q);else{const q=G*Math.sqrt(L*L-1);H=lt=>{const ft=Math.exp(-L*G*lt),W=Math.min(q*lt,300);return h-ft*((B+L*G*Y)*Math.sinh(W)+q*Y*Math.cosh(W))/q}}const it={calculatedDuration:V&&x||null,next:q=>{const lt=H(q);if(V)m.done=q>=x;else{let ft=q===0?B:0;L<1&&(ft=q===0?Qe(B):Rp(H,q,lt));const W=Math.abs(ft)<=o,At=Math.abs(h-lt)<=c;m.done=W&&At}return m.value=m.done?h:lt,m},toString:()=>{const q=Math.min(gc(it),ks),lt=Dp(ft=>it.next(q*ft).value,q,30);return q+"ms "+lt},toTransition:()=>{}};return it}Js.applyToOptions=i=>{const l=$1(i,100,Js);return i.ease=l.ease,i.duration=Qe(l.duration),i.type="keyframes",i};function Hr({keyframes:i,velocity:l=0,power:u=.8,timeConstant:o=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:h,min:m,max:g,restDelta:y=.5,restSpeed:v}){const x=i[0],M={done:!1,value:x},V=W=>m!==void 0&&W<m||g!==void 0&&W>g,B=W=>m===void 0?g:g===void 0||Math.abs(m-W)<Math.abs(g-W)?m:g;let L=u*l;const Y=x+L,G=h===void 0?Y:h(Y);G!==Y&&(L=G-x);const J=W=>-L*Math.exp(-W/o),H=W=>G+J(W),it=W=>{const At=J(W),Lt=H(W);M.done=Math.abs(At)<=y,M.value=M.done?G:Lt};let q,lt;const ft=W=>{V(M.value)&&(q=W,lt=Js({keyframes:[M.value,B(M.value)],velocity:Rp(H,W,M.value),damping:c,stiffness:d,restDelta:y,restSpeed:v}))};return ft(0),{calculatedDuration:null,next:W=>{let At=!1;return!lt&&q===void 0&&(At=!0,it(W),ft(W)),q!==void 0&&W>=q?lt.next(W-q):(!At&&it(W),M)}}}function sb(i,l,u){const o=[],c=u||hn.mix||Ep,d=i.length-1;for(let h=0;h<d;h++){let m=c(i[h],i[h+1]);if(l){const g=Array.isArray(l)?l[h]||ze:l;m=xl(g,m)}o.push(m)}return o}function ub(i,l,{clamp:u=!0,ease:o,mixer:c}={}){const d=i.length;if(uc(d===l.length),d===1)return()=>l[0];if(d===2&&l[0]===l[1])return()=>l[1];const h=i[0]===i[1];i[0]>i[d-1]&&(i=[...i].reverse(),l=[...l].reverse());const m=sb(l,o,c),g=m.length,y=v=>{if(h&&v<i[0])return l[0];let x=0;if(g>1)for(;x<i.length-2&&!(v<i[x+1]);x++);const M=dl(i[x],i[x+1],v);return m[x](M)};return u?v=>y(fn(i[0],i[d-1],v)):y}function ob(i,l){const u=i[i.length-1];for(let o=1;o<=l;o++){const c=dl(0,l,o);i.push(Rt(u,1,c))}}function rb(i){const l=[0];return ob(l,i.length-1),l}function cb(i,l){return i.map(u=>u*l)}function fb(i,l){return i.map(()=>l||yp).splice(0,i.length-1)}function rl({duration:i=300,keyframes:l,times:u,ease:o="easeInOut"}){const c=T1(o)?o.map(Lm):Lm(o),d={done:!1,value:l[0]},h=cb(u&&u.length===l.length?u:rb(l),i),m=ub(h,l,{ease:Array.isArray(c)?c:fb(l,c)});return{calculatedDuration:i,next:g=>(d.value=m(g),d.done=g>=i,d)}}const hb=i=>i!==null;function vc(i,{repeat:l,repeatType:u="loop"},o,c=1){const d=i.filter(hb),m=c<0||l&&u!=="loop"&&l%2===1?0:d.length-1;return!m||o===void 0?d[m]:o}const db={decay:Hr,inertia:Hr,tween:rl,keyframes:rl,spring:Js};function Op(i){typeof i.type=="string"&&(i.type=db[i.type])}class bc{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(l=>{this.resolve=l})}notifyFinished(){this.resolve()}then(l,u){return this.finished.then(l,u)}}const mb=i=>i/100;class xc extends bc{constructor(l){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var o,c;const{motionValue:u}=this.options;u&&u.updatedAt!==fe.now()&&this.tick(fe.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(c=(o=this.options).onStop)==null||c.call(o))},this.options=l,this.initAnimation(),this.play(),l.autoplay===!1&&this.pause()}initAnimation(){const{options:l}=this;Op(l);const{type:u=rl,repeat:o=0,repeatDelay:c=0,repeatType:d,velocity:h=0}=l;let{keyframes:m}=l;const g=u||rl;g!==rl&&typeof m[0]!="number"&&(this.mixKeyframes=xl(mb,Ep(m[0],m[1])),m=[0,100]);const y=g({...l,keyframes:m});d==="mirror"&&(this.mirroredGenerator=g({...l,keyframes:[...m].reverse(),velocity:-h})),y.calculatedDuration===null&&(y.calculatedDuration=gc(y));const{calculatedDuration:v}=y;this.calculatedDuration=v,this.resolvedDuration=v+c,this.totalDuration=this.resolvedDuration*(o+1)-c,this.generator=y}updateTime(l){const u=Math.round(l-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=u}tick(l,u=!1){const{generator:o,totalDuration:c,mixKeyframes:d,mirroredGenerator:h,resolvedDuration:m,calculatedDuration:g}=this;if(this.startTime===null)return o.next(0);const{delay:y=0,keyframes:v,repeat:x,repeatType:M,repeatDelay:V,type:B,onUpdate:L,finalKeyframe:Y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,l):this.speed<0&&(this.startTime=Math.min(l-c/this.speed,this.startTime)),u?this.currentTime=l:this.updateTime(l);const G=this.currentTime-y*(this.playbackSpeed>=0?1:-1),J=this.playbackSpeed>=0?G<0:G>c;this.currentTime=Math.max(G,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let H=this.currentTime,it=o;if(x){const W=Math.min(this.currentTime,c)/m;let At=Math.floor(W),Lt=W%1;!Lt&&W>=1&&(Lt=1),Lt===1&&At--,At=Math.min(At,x+1),!!(At%2)&&(M==="reverse"?(Lt=1-Lt,V&&(Lt-=V/m)):M==="mirror"&&(it=h)),H=fn(0,1,Lt)*m}const q=J?{done:!1,value:v[0]}:it.next(H);d&&(q.value=d(q.value));let{done:lt}=q;!J&&g!==null&&(lt=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const ft=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&lt);return ft&&B!==Hr&&(q.value=vc(v,this.options,Y,this.speed)),L&&L(q.value),ft&&this.finish(),q}then(l,u){return this.finished.then(l,u)}get duration(){return Ke(this.calculatedDuration)}get time(){return Ke(this.currentTime)}set time(l){var u;l=Qe(l),this.currentTime=l,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=l:this.driver&&(this.startTime=this.driver.now()-l/this.playbackSpeed),(u=this.driver)==null||u.start(!1)}get speed(){return this.playbackSpeed}set speed(l){this.updateTime(fe.now());const u=this.playbackSpeed!==l;this.playbackSpeed=l,u&&(this.time=Ke(this.currentTime))}play(){var c,d;if(this.isStopped)return;const{driver:l=W1,startTime:u}=this.options;this.driver||(this.driver=l(h=>this.tick(h))),(d=(c=this.options).onPlay)==null||d.call(c);const o=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=o):this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime||(this.startTime=u??o),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(fe.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var l,u;this.notifyFinished(),this.teardown(),this.state="finished",(u=(l=this.options).onComplete)==null||u.call(l)}cancel(){var l,u;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(u=(l=this.options).onCancel)==null||u.call(l)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(l){return this.startTime=0,this.tick(l,!0)}attachTimeline(l){var u;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(u=this.driver)==null||u.stop(),l.observe(this)}}function yb(i){for(let l=1;l<i.length;l++)i[l]??(i[l]=i[l-1])}const ra=i=>i*180/Math.PI,qr=i=>{const l=ra(Math.atan2(i[1],i[0]));return Yr(l)},pb={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:i=>(Math.abs(i[0])+Math.abs(i[3]))/2,rotate:qr,rotateZ:qr,skewX:i=>ra(Math.atan(i[1])),skewY:i=>ra(Math.atan(i[2])),skew:i=>(Math.abs(i[1])+Math.abs(i[2]))/2},Yr=i=>(i=i%360,i<0&&(i+=360),i),Qm=qr,Km=i=>Math.sqrt(i[0]*i[0]+i[1]*i[1]),km=i=>Math.sqrt(i[4]*i[4]+i[5]*i[5]),gb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Km,scaleY:km,scale:i=>(Km(i)+km(i))/2,rotateX:i=>Yr(ra(Math.atan2(i[6],i[5]))),rotateY:i=>Yr(ra(Math.atan2(-i[2],i[0]))),rotateZ:Qm,rotate:Qm,skewX:i=>ra(Math.atan(i[4])),skewY:i=>ra(Math.atan(i[1])),skew:i=>(Math.abs(i[1])+Math.abs(i[4]))/2};function Gr(i){return i.includes("scale")?1:0}function Xr(i,l){if(!i||i==="none")return Gr(l);const u=i.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let o,c;if(u)o=gb,c=u;else{const m=i.match(/^matrix\(([-\d.e\s,]+)\)$/u);o=pb,c=m}if(!c)return Gr(l);const d=o[l],h=c[1].split(",").map(bb);return typeof d=="function"?d(h):h[d]}const vb=(i,l)=>{const{transform:u="none"}=getComputedStyle(i);return Xr(u,l)};function bb(i){return parseFloat(i.trim())}const li=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],si=new Set(li),Jm=i=>i===ii||i===tt,xb=new Set(["x","y","z"]),Sb=li.filter(i=>!xb.has(i));function Tb(i){const l=[];return Sb.forEach(u=>{const o=i.getValue(u);o!==void 0&&(l.push([u,o.get()]),o.set(u.startsWith("scale")?1:0))}),l}const ca={width:({x:i},{paddingLeft:l="0",paddingRight:u="0"})=>i.max-i.min-parseFloat(l)-parseFloat(u),height:({y:i},{paddingTop:l="0",paddingBottom:u="0"})=>i.max-i.min-parseFloat(l)-parseFloat(u),top:(i,{top:l})=>parseFloat(l),left:(i,{left:l})=>parseFloat(l),bottom:({y:i},{top:l})=>parseFloat(l)+(i.max-i.min),right:({x:i},{left:l})=>parseFloat(l)+(i.max-i.min),x:(i,{transform:l})=>Xr(l,"x"),y:(i,{transform:l})=>Xr(l,"y")};ca.translateX=ca.x;ca.translateY=ca.y;const fa=new Set;let Zr=!1,Qr=!1,Kr=!1;function Np(){if(Qr){const i=Array.from(fa).filter(o=>o.needsMeasurement),l=new Set(i.map(o=>o.element)),u=new Map;l.forEach(o=>{const c=Tb(o);c.length&&(u.set(o,c),o.render())}),i.forEach(o=>o.measureInitialState()),l.forEach(o=>{o.render();const c=u.get(o);c&&c.forEach(([d,h])=>{var m;(m=o.getValue(d))==null||m.set(h)})}),i.forEach(o=>o.measureEndState()),i.forEach(o=>{o.suspendedScrollY!==void 0&&window.scrollTo(0,o.suspendedScrollY)})}Qr=!1,Zr=!1,fa.forEach(i=>i.complete(Kr)),fa.clear()}function Vp(){fa.forEach(i=>{i.readKeyframes(),i.needsMeasurement&&(Qr=!0)})}function Ab(){Kr=!0,Vp(),Np(),Kr=!1}class Sc{constructor(l,u,o,c,d,h=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...l],this.onComplete=u,this.name=o,this.motionValue=c,this.element=d,this.isAsync=h}scheduleResolve(){this.state="scheduled",this.isAsync?(fa.add(this),Zr||(Zr=!0,Ot.read(Vp),Ot.resolveKeyframes(Np))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:l,name:u,element:o,motionValue:c}=this;if(l[0]===null){const d=c==null?void 0:c.get(),h=l[l.length-1];if(d!==void 0)l[0]=d;else if(o&&u){const m=o.readValue(u,h);m!=null&&(l[0]=m)}l[0]===void 0&&(l[0]=h),c&&d===void 0&&c.set(l[0])}yb(l)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(l=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,l),fa.delete(this)}cancel(){this.state==="scheduled"&&(fa.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Mb=i=>i.startsWith("--");function Eb(i,l,u){Mb(l)?i.style.setProperty(l,u):i.style[l]=u}const Db=oc(()=>window.ScrollTimeline!==void 0),Rb={};function Ob(i,l){const u=oc(i);return()=>Rb[l]??u()}const Cp=Ob(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),ul=([i,l,u,o])=>`cubic-bezier(${i}, ${l}, ${u}, ${o})`,Pm={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ul([0,.65,.55,1]),circOut:ul([.55,0,1,.45]),backIn:ul([.31,.01,.66,-.59]),backOut:ul([.33,1.53,.69,.99])};function jp(i,l){if(i)return typeof i=="function"?Cp()?Dp(i,l):"ease-out":pp(i)?ul(i):Array.isArray(i)?i.map(u=>jp(u,l)||Pm.easeOut):Pm[i]}function Nb(i,l,u,{delay:o=0,duration:c=300,repeat:d=0,repeatType:h="loop",ease:m="easeOut",times:g}={},y=void 0){const v={[l]:u};g&&(v.offset=g);const x=jp(m,c);Array.isArray(x)&&(v.easing=x);const M={delay:o,duration:c,easing:Array.isArray(x)?"linear":x,fill:"both",iterations:d+1,direction:h==="reverse"?"alternate":"normal"};return y&&(M.pseudoElement=y),i.animate(v,M)}function zp(i){return typeof i=="function"&&"applyToOptions"in i}function Vb({type:i,...l}){return zp(i)&&Cp()?i.applyToOptions(l):(l.duration??(l.duration=300),l.ease??(l.ease="easeOut"),l)}class Cb extends bc{constructor(l){if(super(),this.finishedTime=null,this.isStopped=!1,!l)return;const{element:u,name:o,keyframes:c,pseudoElement:d,allowFlatten:h=!1,finalKeyframe:m,onComplete:g}=l;this.isPseudoElement=!!d,this.allowFlatten=h,this.options=l,uc(typeof l.type!="string");const y=Vb(l);this.animation=Nb(u,o,c,y,d),y.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const v=vc(c,this.options,m,this.speed);this.updateMotionValue?this.updateMotionValue(v):Eb(u,o,v),this.animation.cancel()}g==null||g(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var l,u;(u=(l=this.animation).finish)==null||u.call(l)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:l}=this;l==="idle"||l==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var l,u;this.isPseudoElement||(u=(l=this.animation).commitStyles)==null||u.call(l)}get duration(){var u,o;const l=((o=(u=this.animation.effect)==null?void 0:u.getComputedTiming)==null?void 0:o.call(u).duration)||0;return Ke(Number(l))}get time(){return Ke(Number(this.animation.currentTime)||0)}set time(l){this.finishedTime=null,this.animation.currentTime=Qe(l)}get speed(){return this.animation.playbackRate}set speed(l){l<0&&(this.finishedTime=null),this.animation.playbackRate=l}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(l){this.animation.startTime=l}attachTimeline({timeline:l,observe:u}){var o;return this.allowFlatten&&((o=this.animation.effect)==null||o.updateTiming({easing:"linear"})),this.animation.onfinish=null,l&&Db()?(this.animation.timeline=l,ze):u(this)}}const _p={anticipate:hp,backInOut:fp,circInOut:mp};function jb(i){return i in _p}function zb(i){typeof i.ease=="string"&&jb(i.ease)&&(i.ease=_p[i.ease])}const Fm=10;class _b extends Cb{constructor(l){zb(l),Op(l),super(l),l.startTime&&(this.startTime=l.startTime),this.options=l}updateMotionValue(l){const{motionValue:u,onUpdate:o,onComplete:c,element:d,...h}=this.options;if(!u)return;if(l!==void 0){u.set(l);return}const m=new xc({...h,autoplay:!1}),g=Qe(this.finishedTime??this.time);u.setWithVelocity(m.sample(g-Fm).value,m.sample(g).value,Fm),m.stop()}}const Wm=(i,l)=>l==="zIndex"?!1:!!(typeof i=="number"||Array.isArray(i)||typeof i=="string"&&(qn.test(i)||i==="0")&&!i.startsWith("url("));function wb(i){const l=i[0];if(i.length===1)return!0;for(let u=0;u<i.length;u++)if(i[u]!==l)return!0}function Ub(i,l,u,o){const c=i[0];if(c===null)return!1;if(l==="display"||l==="visibility")return!0;const d=i[i.length-1],h=Wm(c,l),m=Wm(d,l);return!h||!m?!1:wb(i)||(u==="spring"||zp(u))&&o}function wp(i){return ip(i)&&"offsetHeight"in i}const Bb=new Set(["opacity","clipPath","filter","transform"]),Lb=oc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Hb(i){var y;const{motionValue:l,name:u,repeatDelay:o,repeatType:c,damping:d,type:h}=i;if(!wp((y=l==null?void 0:l.owner)==null?void 0:y.current))return!1;const{onUpdate:m,transformTemplate:g}=l.owner.getProps();return Lb()&&u&&Bb.has(u)&&(u!=="transform"||!g)&&!m&&!o&&c!=="mirror"&&d!==0&&h!=="inertia"}const qb=40;class Yb extends bc{constructor({autoplay:l=!0,delay:u=0,type:o="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:h="loop",keyframes:m,name:g,motionValue:y,element:v,...x}){var B;super(),this.stop=()=>{var L,Y;this._animation&&(this._animation.stop(),(L=this.stopTimeline)==null||L.call(this)),(Y=this.keyframeResolver)==null||Y.cancel()},this.createdAt=fe.now();const M={autoplay:l,delay:u,type:o,repeat:c,repeatDelay:d,repeatType:h,name:g,motionValue:y,element:v,...x},V=(v==null?void 0:v.KeyframeResolver)||Sc;this.keyframeResolver=new V(m,(L,Y,G)=>this.onKeyframesResolved(L,Y,M,!G),g,y,v),(B=this.keyframeResolver)==null||B.scheduleResolve()}onKeyframesResolved(l,u,o,c){this.keyframeResolver=void 0;const{name:d,type:h,velocity:m,delay:g,isHandoff:y,onUpdate:v}=o;this.resolvedAt=fe.now(),Ub(l,d,h,m)||((hn.instantAnimations||!g)&&(v==null||v(vc(l,o,u))),l[0]=l[l.length-1],o.duration=0,o.repeat=0);const M={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>qb?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:u,...o,keyframes:l},V=!y&&Hb(M)?new _b({...M,element:M.motionValue.owner.current}):new xc(M);V.finished.then(()=>this.notifyFinished()).catch(ze),this.pendingTimeline&&(this.stopTimeline=V.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=V}get finished(){return this._animation?this.animation.finished:this._finished}then(l,u){return this.finished.finally(l).then(()=>{})}get animation(){var l;return this._animation||((l=this.keyframeResolver)==null||l.resume(),Ab()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(l){this.animation.time=l}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(l){this.animation.speed=l}get startTime(){return this.animation.startTime}attachTimeline(l){return this._animation?this.stopTimeline=this.animation.attachTimeline(l):this.pendingTimeline=l,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var l;this._animation&&this.animation.cancel(),(l=this.keyframeResolver)==null||l.cancel()}}const Gb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Xb(i){const l=Gb.exec(i);if(!l)return[,];const[,u,o,c]=l;return[`--${u??o}`,c]}function Up(i,l,u=1){const[o,c]=Xb(i);if(!o)return;const d=window.getComputedStyle(l).getPropertyValue(o);if(d){const h=d.trim();return ap(h)?parseFloat(h):h}return dc(c)?Up(c,l,u+1):c}function Tc(i,l){return(i==null?void 0:i[l])??(i==null?void 0:i.default)??i}const Bp=new Set(["width","height","top","left","right","bottom",...li]),Zb={test:i=>i==="auto",parse:i=>i},Lp=i=>l=>l.test(i),Hp=[ii,tt,ke,Ln,w1,_1,Zb],$m=i=>Hp.find(Lp(i));function Qb(i){return typeof i=="number"?i===0:i!==null?i==="none"||i==="0"||lp(i):!0}const Kb=new Set(["brightness","contrast","saturate","opacity"]);function kb(i){const[l,u]=i.slice(0,-1).split("(");if(l==="drop-shadow")return i;const[o]=u.match(mc)||[];if(!o)return i;const c=u.replace(o,"");let d=Kb.has(l)?1:0;return o!==u&&(d*=100),l+"("+d+c+")"}const Jb=/\b([a-z-]*)\(.*?\)/gu,kr={...qn,getAnimatableNone:i=>{const l=i.match(Jb);return l?l.map(kb).join(" "):i}},Im={...ii,transform:Math.round},Pb={rotate:Ln,rotateX:Ln,rotateY:Ln,rotateZ:Ln,scale:qs,scaleX:qs,scaleY:qs,scaleZ:qs,skew:Ln,skewX:Ln,skewY:Ln,distance:tt,translateX:tt,translateY:tt,translateZ:tt,x:tt,y:tt,z:tt,perspective:tt,transformPerspective:tt,opacity:ml,originX:qm,originY:qm,originZ:tt},Ac={borderWidth:tt,borderTopWidth:tt,borderRightWidth:tt,borderBottomWidth:tt,borderLeftWidth:tt,borderRadius:tt,radius:tt,borderTopLeftRadius:tt,borderTopRightRadius:tt,borderBottomRightRadius:tt,borderBottomLeftRadius:tt,width:tt,maxWidth:tt,height:tt,maxHeight:tt,top:tt,right:tt,bottom:tt,left:tt,padding:tt,paddingTop:tt,paddingRight:tt,paddingBottom:tt,paddingLeft:tt,margin:tt,marginTop:tt,marginRight:tt,marginBottom:tt,marginLeft:tt,backgroundPositionX:tt,backgroundPositionY:tt,...Pb,zIndex:Im,fillOpacity:ml,strokeOpacity:ml,numOctaves:Im},Fb={...Ac,color:Bt,backgroundColor:Bt,outlineColor:Bt,fill:Bt,stroke:Bt,borderColor:Bt,borderTopColor:Bt,borderRightColor:Bt,borderBottomColor:Bt,borderLeftColor:Bt,filter:kr,WebkitFilter:kr},qp=i=>Fb[i];function Yp(i,l){let u=qp(i);return u!==kr&&(u=qn),u.getAnimatableNone?u.getAnimatableNone(l):void 0}const Wb=new Set(["auto","none","0"]);function $b(i,l,u){let o=0,c;for(;o<i.length&&!c;){const d=i[o];typeof d=="string"&&!Wb.has(d)&&yl(d).values.length&&(c=i[o]),o++}if(c&&u)for(const d of l)i[d]=Yp(u,c)}class Ib extends Sc{constructor(l,u,o,c,d){super(l,u,o,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:l,element:u,name:o}=this;if(!u||!u.current)return;super.readKeyframes();for(let g=0;g<l.length;g++){let y=l[g];if(typeof y=="string"&&(y=y.trim(),dc(y))){const v=Up(y,u.current);v!==void 0&&(l[g]=v),g===l.length-1&&(this.finalKeyframe=y)}}if(this.resolveNoneKeyframes(),!Bp.has(o)||l.length!==2)return;const[c,d]=l,h=$m(c),m=$m(d);if(h!==m)if(Jm(h)&&Jm(m))for(let g=0;g<l.length;g++){const y=l[g];typeof y=="string"&&(l[g]=parseFloat(y))}else ca[o]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:l,name:u}=this,o=[];for(let c=0;c<l.length;c++)(l[c]===null||Qb(l[c]))&&o.push(c);o.length&&$b(l,o,u)}measureInitialState(){const{element:l,unresolvedKeyframes:u,name:o}=this;if(!l||!l.current)return;o==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ca[o](l.measureViewportBox(),window.getComputedStyle(l.current)),u[0]=this.measuredOrigin;const c=u[u.length-1];c!==void 0&&l.getValue(o,c).jump(c,!1)}measureEndState(){var m;const{element:l,name:u,unresolvedKeyframes:o}=this;if(!l||!l.current)return;const c=l.getValue(u);c&&c.jump(this.measuredOrigin,!1);const d=o.length-1,h=o[d];o[d]=ca[u](l.measureViewportBox(),window.getComputedStyle(l.current)),h!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=h),(m=this.removedTransforms)!=null&&m.length&&this.removedTransforms.forEach(([g,y])=>{l.getValue(g).set(y)}),this.resolveNoneKeyframes()}}function tx(i,l,u){if(i instanceof EventTarget)return[i];if(typeof i=="string"){let o=document;const c=(u==null?void 0:u[i])??o.querySelectorAll(i);return c?Array.from(c):[]}return Array.from(i)}const Gp=(i,l)=>l&&typeof i=="number"?l.transform(i):i,ty=30,ex=i=>!isNaN(parseFloat(i));class nx{constructor(l,u={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(o,c=!0)=>{var h,m;const d=fe.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(o),this.current!==this.prev&&((h=this.events.change)==null||h.notify(this.current),this.dependents))for(const g of this.dependents)g.dirty();c&&((m=this.events.renderRequest)==null||m.notify(this.current))},this.hasAnimated=!1,this.setCurrent(l),this.owner=u.owner}setCurrent(l){this.current=l,this.updatedAt=fe.now(),this.canTrackVelocity===null&&l!==void 0&&(this.canTrackVelocity=ex(this.current))}setPrevFrameValue(l=this.current){this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt}onChange(l){return this.on("change",l)}on(l,u){this.events[l]||(this.events[l]=new rc);const o=this.events[l].add(u);return l==="change"?()=>{o(),Ot.read(()=>{this.events.change.getSize()||this.stop()})}:o}clearListeners(){for(const l in this.events)this.events[l].clear()}attach(l,u){this.passiveEffect=l,this.stopPassiveEffect=u}set(l,u=!0){!u||!this.passiveEffect?this.updateAndNotify(l,u):this.passiveEffect(l,this.updateAndNotify)}setWithVelocity(l,u,o){this.set(u),this.prev=void 0,this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt-o}jump(l,u=!0){this.updateAndNotify(l),this.prev=l,this.prevUpdatedAt=this.prevFrameValue=void 0,u&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var l;(l=this.events.change)==null||l.notify(this.current)}addDependent(l){this.dependents||(this.dependents=new Set),this.dependents.add(l)}removeDependent(l){this.dependents&&this.dependents.delete(l)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const l=fe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||l-this.updatedAt>ty)return 0;const u=Math.min(this.updatedAt-this.prevUpdatedAt,ty);return sp(parseFloat(this.current)-parseFloat(this.prevFrameValue),u)}start(l){return this.stop(),new Promise(u=>{this.hasAnimated=!0,this.animation=l(u),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var l,u;(l=this.dependents)==null||l.clear(),(u=this.events.destroy)==null||u.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ni(i,l){return new nx(i,l)}const{schedule:Mc}=gp(queueMicrotask,!1),Be={x:!1,y:!1};function Xp(){return Be.x||Be.y}function ax(i){return i==="x"||i==="y"?Be[i]?null:(Be[i]=!0,()=>{Be[i]=!1}):Be.x||Be.y?null:(Be.x=Be.y=!0,()=>{Be.x=Be.y=!1})}function Zp(i,l){const u=tx(i),o=new AbortController,c={passive:!0,...l,signal:o.signal};return[u,c,()=>o.abort()]}function ey(i){return!(i.pointerType==="touch"||Xp())}function ix(i,l,u={}){const[o,c,d]=Zp(i,u),h=m=>{if(!ey(m))return;const{target:g}=m,y=l(g,m);if(typeof y!="function"||!g)return;const v=x=>{ey(x)&&(y(x),g.removeEventListener("pointerleave",v))};g.addEventListener("pointerleave",v,c)};return o.forEach(m=>{m.addEventListener("pointerenter",h,c)}),d}const Qp=(i,l)=>l?i===l?!0:Qp(i,l.parentElement):!1,Ec=i=>i.pointerType==="mouse"?typeof i.button!="number"||i.button<=0:i.isPrimary!==!1,lx=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function sx(i){return lx.has(i.tagName)||i.tabIndex!==-1}const Xs=new WeakSet;function ny(i){return l=>{l.key==="Enter"&&i(l)}}function Rr(i,l){i.dispatchEvent(new PointerEvent("pointer"+l,{isPrimary:!0,bubbles:!0}))}const ux=(i,l)=>{const u=i.currentTarget;if(!u)return;const o=ny(()=>{if(Xs.has(u))return;Rr(u,"down");const c=ny(()=>{Rr(u,"up")}),d=()=>Rr(u,"cancel");u.addEventListener("keyup",c,l),u.addEventListener("blur",d,l)});u.addEventListener("keydown",o,l),u.addEventListener("blur",()=>u.removeEventListener("keydown",o),l)};function ay(i){return Ec(i)&&!Xp()}function ox(i,l,u={}){const[o,c,d]=Zp(i,u),h=m=>{const g=m.currentTarget;if(!ay(m))return;Xs.add(g);const y=l(g,m),v=(V,B)=>{window.removeEventListener("pointerup",x),window.removeEventListener("pointercancel",M),Xs.has(g)&&Xs.delete(g),ay(V)&&typeof y=="function"&&y(V,{success:B})},x=V=>{v(V,g===window||g===document||u.useGlobalTarget||Qp(g,V.target))},M=V=>{v(V,!1)};window.addEventListener("pointerup",x,c),window.addEventListener("pointercancel",M,c)};return o.forEach(m=>{(u.useGlobalTarget?window:m).addEventListener("pointerdown",h,c),wp(m)&&(m.addEventListener("focus",y=>ux(y,c)),!sx(m)&&!m.hasAttribute("tabindex")&&(m.tabIndex=0))}),d}function Kp(i){return ip(i)&&"ownerSVGElement"in i}function rx(i){return Kp(i)&&i.tagName==="svg"}const ae=i=>!!(i&&i.getVelocity),cx=[...Hp,Bt,qn],fx=i=>cx.find(Lp(i)),kp=et.createContext({transformPagePoint:i=>i,isStatic:!1,reducedMotion:"never"});function hx(i=!0){const l=et.useContext(ic);if(l===null)return[!0,null];const{isPresent:u,onExitComplete:o,register:c}=l,d=et.useId();et.useEffect(()=>{if(i)return c(d)},[i]);const h=et.useCallback(()=>i&&o&&o(d),[d,o,i]);return!u&&o?[!1,h]:[!0]}const Jp=et.createContext({strict:!1}),iy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ai={};for(const i in iy)ai[i]={isEnabled:l=>iy[i].some(u=>!!l[u])};function dx(i){for(const l in i)ai[l]={...ai[l],...i[l]}}const mx=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ps(i){return i.startsWith("while")||i.startsWith("drag")&&i!=="draggable"||i.startsWith("layout")||i.startsWith("onTap")||i.startsWith("onPan")||i.startsWith("onLayout")||mx.has(i)}let Pp=i=>!Ps(i);function yx(i){typeof i=="function"&&(Pp=l=>l.startsWith("on")?!Ps(l):i(l))}try{yx(require("@emotion/is-prop-valid").default)}catch{}function px(i,l,u){const o={};for(const c in i)c==="values"&&typeof i.values=="object"||(Pp(c)||u===!0&&Ps(c)||!l&&!Ps(c)||i.draggable&&c.startsWith("onDrag"))&&(o[c]=i[c]);return o}function gx(i){if(typeof Proxy>"u")return i;const l=new Map,u=(...o)=>i(...o);return new Proxy(u,{get:(o,c)=>c==="create"?i:(l.has(c)||l.set(c,i(c)),l.get(c))})}const Ws=et.createContext({});function $s(i){return i!==null&&typeof i=="object"&&typeof i.start=="function"}function pl(i){return typeof i=="string"||Array.isArray(i)}const Dc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Rc=["initial",...Dc];function Is(i){return $s(i.animate)||Rc.some(l=>pl(i[l]))}function Fp(i){return!!(Is(i)||i.variants)}function vx(i,l){if(Is(i)){const{initial:u,animate:o}=i;return{initial:u===!1||pl(u)?u:void 0,animate:pl(o)?o:void 0}}return i.inherit!==!1?l:{}}function bx(i){const{initial:l,animate:u}=vx(i,et.useContext(Ws));return et.useMemo(()=>({initial:l,animate:u}),[ly(l),ly(u)])}function ly(i){return Array.isArray(i)?i.join(" "):i}const xx=Symbol.for("motionComponentSymbol");function $a(i){return i&&typeof i=="object"&&Object.prototype.hasOwnProperty.call(i,"current")}function Sx(i,l,u){return et.useCallback(o=>{o&&i.onMount&&i.onMount(o),l&&(o?l.mount(o):l.unmount()),u&&(typeof u=="function"?u(o):$a(u)&&(u.current=o))},[l])}const Oc=i=>i.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Tx="framerAppearId",Wp="data-"+Oc(Tx),$p=et.createContext({});function Ax(i,l,u,o,c){var L,Y;const{visualElement:d}=et.useContext(Ws),h=et.useContext(Jp),m=et.useContext(ic),g=et.useContext(kp).reducedMotion,y=et.useRef(null);o=o||h.renderer,!y.current&&o&&(y.current=o(i,{visualState:l,parent:d,props:u,presenceContext:m,blockInitialAnimation:m?m.initial===!1:!1,reducedMotionConfig:g}));const v=y.current,x=et.useContext($p);v&&!v.projection&&c&&(v.type==="html"||v.type==="svg")&&Mx(y.current,u,c,x);const M=et.useRef(!1);et.useInsertionEffect(()=>{v&&M.current&&v.update(u,m)});const V=u[Wp],B=et.useRef(!!V&&!((L=window.MotionHandoffIsComplete)!=null&&L.call(window,V))&&((Y=window.MotionHasOptimisedAnimation)==null?void 0:Y.call(window,V)));return y1(()=>{v&&(M.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),Mc.render(v.render),B.current&&v.animationState&&v.animationState.animateChanges())}),et.useEffect(()=>{v&&(!B.current&&v.animationState&&v.animationState.animateChanges(),B.current&&(queueMicrotask(()=>{var G;(G=window.MotionHandoffMarkAsComplete)==null||G.call(window,V)}),B.current=!1))}),v}function Mx(i,l,u,o){const{layoutId:c,layout:d,drag:h,dragConstraints:m,layoutScroll:g,layoutRoot:y,layoutCrossfade:v}=l;i.projection=new u(i.latestValues,l["data-framer-portal-id"]?void 0:Ip(i.parent)),i.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!h||m&&$a(m),visualElement:i,animationType:typeof d=="string"?d:"both",initialPromotionConfig:o,crossfade:v,layoutScroll:g,layoutRoot:y})}function Ip(i){if(i)return i.options.allowProjection!==!1?i.projection:Ip(i.parent)}function Ex({preloadedFeatures:i,createVisualElement:l,useRender:u,useVisualState:o,Component:c}){i&&dx(i);function d(m,g){let y;const v={...et.useContext(kp),...m,layoutId:Dx(m)},{isStatic:x}=v,M=bx(m),V=o(m,x);if(!x&&ac){Rx();const B=Ox(v);y=B.MeasureLayout,M.visualElement=Ax(c,V,v,l,B.ProjectionNode)}return R.jsxs(Ws.Provider,{value:M,children:[y&&M.visualElement?R.jsx(y,{visualElement:M.visualElement,...v}):null,u(c,m,Sx(V,M.visualElement,g),V,x,M.visualElement)]})}d.displayName=`motion.${typeof c=="string"?c:`create(${c.displayName??c.name??""})`}`;const h=et.forwardRef(d);return h[xx]=c,h}function Dx({layoutId:i}){const l=et.useContext(np).id;return l&&i!==void 0?l+"-"+i:i}function Rx(i,l){et.useContext(Jp).strict}function Ox(i){const{drag:l,layout:u}=ai;if(!l&&!u)return{};const o={...l,...u};return{MeasureLayout:l!=null&&l.isEnabled(i)||u!=null&&u.isEnabled(i)?o.MeasureLayout:void 0,ProjectionNode:o.ProjectionNode}}const gl={};function Nx(i){for(const l in i)gl[l]=i[l],hc(l)&&(gl[l].isCSSVariable=!0)}function t0(i,{layout:l,layoutId:u}){return si.has(i)||i.startsWith("origin")||(l||u!==void 0)&&(!!gl[i]||i==="opacity")}const Vx={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Cx=li.length;function jx(i,l,u){let o="",c=!0;for(let d=0;d<Cx;d++){const h=li[d],m=i[h];if(m===void 0)continue;let g=!0;if(typeof m=="number"?g=m===(h.startsWith("scale")?1:0):g=parseFloat(m)===0,!g||u){const y=Gp(m,Ac[h]);if(!g){c=!1;const v=Vx[h]||h;o+=`${v}(${y}) `}u&&(l[h]=y)}}return o=o.trim(),u?o=u(l,c?"":o):c&&(o="none"),o}function Nc(i,l,u){const{style:o,vars:c,transformOrigin:d}=i;let h=!1,m=!1;for(const g in l){const y=l[g];if(si.has(g)){h=!0;continue}else if(hc(g)){c[g]=y;continue}else{const v=Gp(y,Ac[g]);g.startsWith("origin")?(m=!0,d[g]=v):o[g]=v}}if(l.transform||(h||u?o.transform=jx(l,i.transform,u):o.transform&&(o.transform="none")),m){const{originX:g="50%",originY:y="50%",originZ:v=0}=d;o.transformOrigin=`${g} ${y} ${v}`}}const Vc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function e0(i,l,u){for(const o in l)!ae(l[o])&&!t0(o,u)&&(i[o]=l[o])}function zx({transformTemplate:i},l){return et.useMemo(()=>{const u=Vc();return Nc(u,l,i),Object.assign({},u.vars,u.style)},[l])}function _x(i,l){const u=i.style||{},o={};return e0(o,u,i),Object.assign(o,zx(i,l)),o}function wx(i,l){const u={},o=_x(i,l);return i.drag&&i.dragListener!==!1&&(u.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=i.drag===!0?"none":`pan-${i.drag==="x"?"y":"x"}`),i.tabIndex===void 0&&(i.onTap||i.onTapStart||i.whileTap)&&(u.tabIndex=0),u.style=o,u}const Ux={offset:"stroke-dashoffset",array:"stroke-dasharray"},Bx={offset:"strokeDashoffset",array:"strokeDasharray"};function Lx(i,l,u=1,o=0,c=!0){i.pathLength=1;const d=c?Ux:Bx;i[d.offset]=tt.transform(-o);const h=tt.transform(l),m=tt.transform(u);i[d.array]=`${h} ${m}`}function n0(i,{attrX:l,attrY:u,attrScale:o,pathLength:c,pathSpacing:d=1,pathOffset:h=0,...m},g,y,v){if(Nc(i,m,y),g){i.style.viewBox&&(i.attrs.viewBox=i.style.viewBox);return}i.attrs=i.style,i.style={};const{attrs:x,style:M}=i;x.transform&&(M.transform=x.transform,delete x.transform),(M.transform||x.transformOrigin)&&(M.transformOrigin=x.transformOrigin??"50% 50%",delete x.transformOrigin),M.transform&&(M.transformBox=(v==null?void 0:v.transformBox)??"fill-box",delete x.transformBox),l!==void 0&&(x.x=l),u!==void 0&&(x.y=u),o!==void 0&&(x.scale=o),c!==void 0&&Lx(x,c,d,h,!1)}const a0=()=>({...Vc(),attrs:{}}),i0=i=>typeof i=="string"&&i.toLowerCase()==="svg";function Hx(i,l,u,o){const c=et.useMemo(()=>{const d=a0();return n0(d,l,i0(o),i.transformTemplate,i.style),{...d.attrs,style:{...d.style}}},[l]);if(i.style){const d={};e0(d,i.style,i),c.style={...d,...c.style}}return c}const qx=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Cc(i){return typeof i!="string"||i.includes("-")?!1:!!(qx.indexOf(i)>-1||/[A-Z]/u.test(i))}function Yx(i=!1){return(u,o,c,{latestValues:d},h)=>{const g=(Cc(u)?Hx:wx)(o,d,h,u),y=px(o,typeof u=="string",i),v=u!==et.Fragment?{...y,...g,ref:c}:{},{children:x}=o,M=et.useMemo(()=>ae(x)?x.get():x,[x]);return et.createElement(u,{...v,children:M})}}function sy(i){const l=[{},{}];return i==null||i.values.forEach((u,o)=>{l[0][o]=u.get(),l[1][o]=u.getVelocity()}),l}function jc(i,l,u,o){if(typeof l=="function"){const[c,d]=sy(o);l=l(u!==void 0?u:i.custom,c,d)}if(typeof l=="string"&&(l=i.variants&&i.variants[l]),typeof l=="function"){const[c,d]=sy(o);l=l(u!==void 0?u:i.custom,c,d)}return l}function Zs(i){return ae(i)?i.get():i}function Gx({scrapeMotionValuesFromProps:i,createRenderState:l},u,o,c){return{latestValues:Xx(u,o,c,i),renderState:l()}}const l0=i=>(l,u)=>{const o=et.useContext(Ws),c=et.useContext(ic),d=()=>Gx(i,l,o,c);return u?d():m1(d)};function Xx(i,l,u,o){const c={},d=o(i,{});for(const M in d)c[M]=Zs(d[M]);let{initial:h,animate:m}=i;const g=Is(i),y=Fp(i);l&&y&&!g&&i.inherit!==!1&&(h===void 0&&(h=l.initial),m===void 0&&(m=l.animate));let v=u?u.initial===!1:!1;v=v||h===!1;const x=v?m:h;if(x&&typeof x!="boolean"&&!$s(x)){const M=Array.isArray(x)?x:[x];for(let V=0;V<M.length;V++){const B=jc(i,M[V]);if(B){const{transitionEnd:L,transition:Y,...G}=B;for(const J in G){let H=G[J];if(Array.isArray(H)){const it=v?H.length-1:0;H=H[it]}H!==null&&(c[J]=H)}for(const J in L)c[J]=L[J]}}}return c}function zc(i,l,u){var d;const{style:o}=i,c={};for(const h in o)(ae(o[h])||l.style&&ae(l.style[h])||t0(h,i)||((d=u==null?void 0:u.getValue(h))==null?void 0:d.liveStyle)!==void 0)&&(c[h]=o[h]);return c}const Zx={useVisualState:l0({scrapeMotionValuesFromProps:zc,createRenderState:Vc})};function s0(i,l,u){const o=zc(i,l,u);for(const c in i)if(ae(i[c])||ae(l[c])){const d=li.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;o[d]=i[c]}return o}const Qx={useVisualState:l0({scrapeMotionValuesFromProps:s0,createRenderState:a0})};function Kx(i,l){return function(o,{forwardMotionProps:c}={forwardMotionProps:!1}){const h={...Cc(o)?Qx:Zx,preloadedFeatures:i,useRender:Yx(c),createVisualElement:l,Component:o};return Ex(h)}}function vl(i,l,u){const o=i.getProps();return jc(o,l,u!==void 0?u:o.custom,i)}const Jr=i=>Array.isArray(i);function kx(i,l,u){i.hasValue(l)?i.getValue(l).set(u):i.addValue(l,ni(u))}function Jx(i){return Jr(i)?i[i.length-1]||0:i}function Px(i,l){const u=vl(i,l);let{transitionEnd:o={},transition:c={},...d}=u||{};d={...d,...o};for(const h in d){const m=Jx(d[h]);kx(i,h,m)}}function Fx(i){return!!(ae(i)&&i.add)}function Pr(i,l){const u=i.getValue("willChange");if(Fx(u))return u.add(l);if(!u&&hn.WillChange){const o=new hn.WillChange("auto");i.addValue("willChange",o),o.add(l)}}function u0(i){return i.props[Wp]}const Wx=i=>i!==null;function $x(i,{repeat:l,repeatType:u="loop"},o){const c=i.filter(Wx),d=l&&u!=="loop"&&l%2===1?0:c.length-1;return c[d]}const Ix={type:"spring",stiffness:500,damping:25,restSpeed:10},tS=i=>({type:"spring",stiffness:550,damping:i===0?2*Math.sqrt(550):30,restSpeed:10}),eS={type:"keyframes",duration:.8},nS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},aS=(i,{keyframes:l})=>l.length>2?eS:si.has(i)?i.startsWith("scale")?tS(l[1]):Ix:nS;function iS({when:i,delay:l,delayChildren:u,staggerChildren:o,staggerDirection:c,repeat:d,repeatType:h,repeatDelay:m,from:g,elapsed:y,...v}){return!!Object.keys(v).length}const _c=(i,l,u,o={},c,d)=>h=>{const m=Tc(o,i)||{},g=m.delay||o.delay||0;let{elapsed:y=0}=o;y=y-Qe(g);const v={keyframes:Array.isArray(u)?u:[null,u],ease:"easeOut",velocity:l.getVelocity(),...m,delay:-y,onUpdate:M=>{l.set(M),m.onUpdate&&m.onUpdate(M)},onComplete:()=>{h(),m.onComplete&&m.onComplete()},name:i,motionValue:l,element:d?void 0:c};iS(m)||Object.assign(v,aS(i,v)),v.duration&&(v.duration=Qe(v.duration)),v.repeatDelay&&(v.repeatDelay=Qe(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let x=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(v.duration=0,v.delay===0&&(x=!0)),(hn.instantAnimations||hn.skipAnimations)&&(x=!0,v.duration=0,v.delay=0),v.allowFlatten=!m.type&&!m.ease,x&&!d&&l.get()!==void 0){const M=$x(v.keyframes,m);if(M!==void 0){Ot.update(()=>{v.onUpdate(M),v.onComplete()});return}}return m.isSync?new xc(v):new Yb(v)};function lS({protectedKeys:i,needsAnimating:l},u){const o=i.hasOwnProperty(u)&&l[u]!==!0;return l[u]=!1,o}function o0(i,l,{delay:u=0,transitionOverride:o,type:c}={}){let{transition:d=i.getDefaultTransition(),transitionEnd:h,...m}=l;o&&(d=o);const g=[],y=c&&i.animationState&&i.animationState.getState()[c];for(const v in m){const x=i.getValue(v,i.latestValues[v]??null),M=m[v];if(M===void 0||y&&lS(y,v))continue;const V={delay:u,...Tc(d||{},v)},B=x.get();if(B!==void 0&&!x.isAnimating&&!Array.isArray(M)&&M===B&&!V.velocity)continue;let L=!1;if(window.MotionHandoffAnimation){const G=u0(i);if(G){const J=window.MotionHandoffAnimation(G,v,Ot);J!==null&&(V.startTime=J,L=!0)}}Pr(i,v),x.start(_c(v,x,M,i.shouldReduceMotion&&Bp.has(v)?{type:!1}:V,i,L));const Y=x.animation;Y&&g.push(Y)}return h&&Promise.all(g).then(()=>{Ot.update(()=>{h&&Px(i,h)})}),g}function Fr(i,l,u={}){var g;const o=vl(i,l,u.type==="exit"?(g=i.presenceContext)==null?void 0:g.custom:void 0);let{transition:c=i.getDefaultTransition()||{}}=o||{};u.transitionOverride&&(c=u.transitionOverride);const d=o?()=>Promise.all(o0(i,o,u)):()=>Promise.resolve(),h=i.variantChildren&&i.variantChildren.size?(y=0)=>{const{delayChildren:v=0,staggerChildren:x,staggerDirection:M}=c;return sS(i,l,v+y,x,M,u)}:()=>Promise.resolve(),{when:m}=c;if(m){const[y,v]=m==="beforeChildren"?[d,h]:[h,d];return y().then(()=>v())}else return Promise.all([d(),h(u.delay)])}function sS(i,l,u=0,o=0,c=1,d){const h=[],m=(i.variantChildren.size-1)*o,g=c===1?(y=0)=>y*o:(y=0)=>m-y*o;return Array.from(i.variantChildren).sort(uS).forEach((y,v)=>{y.notify("AnimationStart",l),h.push(Fr(y,l,{...d,delay:u+g(v)}).then(()=>y.notify("AnimationComplete",l)))}),Promise.all(h)}function uS(i,l){return i.sortNodePosition(l)}function oS(i,l,u={}){i.notify("AnimationStart",l);let o;if(Array.isArray(l)){const c=l.map(d=>Fr(i,d,u));o=Promise.all(c)}else if(typeof l=="string")o=Fr(i,l,u);else{const c=typeof l=="function"?vl(i,l,u.custom):l;o=Promise.all(o0(i,c,u))}return o.then(()=>{i.notify("AnimationComplete",l)})}function r0(i,l){if(!Array.isArray(l))return!1;const u=l.length;if(u!==i.length)return!1;for(let o=0;o<u;o++)if(l[o]!==i[o])return!1;return!0}const rS=Rc.length;function c0(i){if(!i)return;if(!i.isControllingVariants){const u=i.parent?c0(i.parent)||{}:{};return i.props.initial!==void 0&&(u.initial=i.props.initial),u}const l={};for(let u=0;u<rS;u++){const o=Rc[u],c=i.props[o];(pl(c)||c===!1)&&(l[o]=c)}return l}const cS=[...Dc].reverse(),fS=Dc.length;function hS(i){return l=>Promise.all(l.map(({animation:u,options:o})=>oS(i,u,o)))}function dS(i){let l=hS(i),u=uy(),o=!0;const c=g=>(y,v)=>{var M;const x=vl(i,v,g==="exit"?(M=i.presenceContext)==null?void 0:M.custom:void 0);if(x){const{transition:V,transitionEnd:B,...L}=x;y={...y,...L,...B}}return y};function d(g){l=g(i)}function h(g){const{props:y}=i,v=c0(i.parent)||{},x=[],M=new Set;let V={},B=1/0;for(let Y=0;Y<fS;Y++){const G=cS[Y],J=u[G],H=y[G]!==void 0?y[G]:v[G],it=pl(H),q=G===g?J.isActive:null;q===!1&&(B=Y);let lt=H===v[G]&&H!==y[G]&&it;if(lt&&o&&i.manuallyAnimateOnMount&&(lt=!1),J.protectedKeys={...V},!J.isActive&&q===null||!H&&!J.prevProp||$s(H)||typeof H=="boolean")continue;const ft=mS(J.prevProp,H);let W=ft||G===g&&J.isActive&&!lt&&it||Y>B&&it,At=!1;const Lt=Array.isArray(H)?H:[H];let It=Lt.reduce(c(G),{});q===!1&&(It={});const{prevResolvedValues:Ht={}}=J,Je={...Ht,...It},Le=U=>{W=!0,M.has(U)&&(At=!0,M.delete(U)),J.needsAnimating[U]=!0;const K=i.getValue(U);K&&(K.liveStyle=!1)};for(const U in Je){const K=It[U],mt=Ht[U];if(V.hasOwnProperty(U))continue;let S=!1;Jr(K)&&Jr(mt)?S=!r0(K,mt):S=K!==mt,S?K!=null?Le(U):M.add(U):K!==void 0&&M.has(U)?Le(U):J.protectedKeys[U]=!0}J.prevProp=H,J.prevResolvedValues=It,J.isActive&&(V={...V,...It}),o&&i.blockInitialAnimation&&(W=!1),W&&(!(lt&&ft)||At)&&x.push(...Lt.map(U=>({animation:U,options:{type:G}})))}if(M.size){const Y={};if(typeof y.initial!="boolean"){const G=vl(i,Array.isArray(y.initial)?y.initial[0]:y.initial);G&&G.transition&&(Y.transition=G.transition)}M.forEach(G=>{const J=i.getBaseTarget(G),H=i.getValue(G);H&&(H.liveStyle=!0),Y[G]=J??null}),x.push({animation:Y})}let L=!!x.length;return o&&(y.initial===!1||y.initial===y.animate)&&!i.manuallyAnimateOnMount&&(L=!1),o=!1,L?l(x):Promise.resolve()}function m(g,y){var x;if(u[g].isActive===y)return Promise.resolve();(x=i.variantChildren)==null||x.forEach(M=>{var V;return(V=M.animationState)==null?void 0:V.setActive(g,y)}),u[g].isActive=y;const v=h(g);for(const M in u)u[M].protectedKeys={};return v}return{animateChanges:h,setActive:m,setAnimateFunction:d,getState:()=>u,reset:()=>{u=uy(),o=!0}}}function mS(i,l){return typeof l=="string"?l!==i:Array.isArray(l)?!r0(l,i):!1}function sa(i=!1){return{isActive:i,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function uy(){return{animate:sa(!0),whileInView:sa(),whileHover:sa(),whileTap:sa(),whileDrag:sa(),whileFocus:sa(),exit:sa()}}class Yn{constructor(l){this.isMounted=!1,this.node=l}update(){}}class yS extends Yn{constructor(l){super(l),l.animationState||(l.animationState=dS(l))}updateAnimationControlsSubscription(){const{animate:l}=this.node.getProps();$s(l)&&(this.unmountControls=l.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:l}=this.node.getProps(),{animate:u}=this.node.prevProps||{};l!==u&&this.updateAnimationControlsSubscription()}unmount(){var l;this.node.animationState.reset(),(l=this.unmountControls)==null||l.call(this)}}let pS=0;class gS extends Yn{constructor(){super(...arguments),this.id=pS++}update(){if(!this.node.presenceContext)return;const{isPresent:l,onExitComplete:u}=this.node.presenceContext,{isPresent:o}=this.node.prevPresenceContext||{};if(!this.node.animationState||l===o)return;const c=this.node.animationState.setActive("exit",!l);u&&!l&&c.then(()=>{u(this.id)})}mount(){const{register:l,onExitComplete:u}=this.node.presenceContext||{};u&&u(this.id),l&&(this.unmount=l(this.id))}unmount(){}}const vS={animation:{Feature:yS},exit:{Feature:gS}};function bl(i,l,u,o={passive:!0}){return i.addEventListener(l,u,o),()=>i.removeEventListener(l,u)}function Al(i){return{point:{x:i.pageX,y:i.pageY}}}const bS=i=>l=>Ec(l)&&i(l,Al(l));function cl(i,l,u,o){return bl(i,l,bS(u),o)}function f0({top:i,left:l,right:u,bottom:o}){return{x:{min:l,max:u},y:{min:i,max:o}}}function xS({x:i,y:l}){return{top:l.min,right:i.max,bottom:l.max,left:i.min}}function SS(i,l){if(!l)return i;const u=l({x:i.left,y:i.top}),o=l({x:i.right,y:i.bottom});return{top:u.y,left:u.x,bottom:o.y,right:o.x}}const h0=1e-4,TS=1-h0,AS=1+h0,d0=.01,MS=0-d0,ES=0+d0;function le(i){return i.max-i.min}function DS(i,l,u){return Math.abs(i-l)<=u}function oy(i,l,u,o=.5){i.origin=o,i.originPoint=Rt(l.min,l.max,i.origin),i.scale=le(u)/le(l),i.translate=Rt(u.min,u.max,i.origin)-i.originPoint,(i.scale>=TS&&i.scale<=AS||isNaN(i.scale))&&(i.scale=1),(i.translate>=MS&&i.translate<=ES||isNaN(i.translate))&&(i.translate=0)}function fl(i,l,u,o){oy(i.x,l.x,u.x,o?o.originX:void 0),oy(i.y,l.y,u.y,o?o.originY:void 0)}function ry(i,l,u){i.min=u.min+l.min,i.max=i.min+le(l)}function RS(i,l,u){ry(i.x,l.x,u.x),ry(i.y,l.y,u.y)}function cy(i,l,u){i.min=l.min-u.min,i.max=i.min+le(l)}function hl(i,l,u){cy(i.x,l.x,u.x),cy(i.y,l.y,u.y)}const fy=()=>({translate:0,scale:1,origin:0,originPoint:0}),Ia=()=>({x:fy(),y:fy()}),hy=()=>({min:0,max:0}),_t=()=>({x:hy(),y:hy()});function je(i){return[i("x"),i("y")]}function Or(i){return i===void 0||i===1}function Wr({scale:i,scaleX:l,scaleY:u}){return!Or(i)||!Or(l)||!Or(u)}function ua(i){return Wr(i)||m0(i)||i.z||i.rotate||i.rotateX||i.rotateY||i.skewX||i.skewY}function m0(i){return dy(i.x)||dy(i.y)}function dy(i){return i&&i!=="0%"}function Fs(i,l,u){const o=i-u,c=l*o;return u+c}function my(i,l,u,o,c){return c!==void 0&&(i=Fs(i,c,o)),Fs(i,u,o)+l}function $r(i,l=0,u=1,o,c){i.min=my(i.min,l,u,o,c),i.max=my(i.max,l,u,o,c)}function y0(i,{x:l,y:u}){$r(i.x,l.translate,l.scale,l.originPoint),$r(i.y,u.translate,u.scale,u.originPoint)}const yy=.999999999999,py=1.0000000000001;function OS(i,l,u,o=!1){const c=u.length;if(!c)return;l.x=l.y=1;let d,h;for(let m=0;m<c;m++){d=u[m],h=d.projectionDelta;const{visualElement:g}=d.options;g&&g.props.style&&g.props.style.display==="contents"||(o&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ei(i,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),h&&(l.x*=h.x.scale,l.y*=h.y.scale,y0(i,h)),o&&ua(d.latestValues)&&ei(i,d.latestValues))}l.x<py&&l.x>yy&&(l.x=1),l.y<py&&l.y>yy&&(l.y=1)}function ti(i,l){i.min=i.min+l,i.max=i.max+l}function gy(i,l,u,o,c=.5){const d=Rt(i.min,i.max,c);$r(i,l,u,d,o)}function ei(i,l){gy(i.x,l.x,l.scaleX,l.scale,l.originX),gy(i.y,l.y,l.scaleY,l.scale,l.originY)}function p0(i,l){return f0(SS(i.getBoundingClientRect(),l))}function NS(i,l,u){const o=p0(i,u),{scroll:c}=l;return c&&(ti(o.x,c.offset.x),ti(o.y,c.offset.y)),o}const g0=({current:i})=>i?i.ownerDocument.defaultView:null,vy=(i,l)=>Math.abs(i-l);function VS(i,l){const u=vy(i.x,l.x),o=vy(i.y,l.y);return Math.sqrt(u**2+o**2)}class v0{constructor(l,u,{transformPagePoint:o,contextWindow:c,dragSnapToOrigin:d=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=Vr(this.lastMoveEventInfo,this.history),M=this.startEvent!==null,V=VS(x.offset,{x:0,y:0})>=3;if(!M&&!V)return;const{point:B}=x,{timestamp:L}=$t;this.history.push({...B,timestamp:L});const{onStart:Y,onMove:G}=this.handlers;M||(Y&&Y(this.lastMoveEvent,x),this.startEvent=this.lastMoveEvent),G&&G(this.lastMoveEvent,x)},this.handlePointerMove=(x,M)=>{this.lastMoveEvent=x,this.lastMoveEventInfo=Nr(M,this.transformPagePoint),Ot.update(this.updatePoint,!0)},this.handlePointerUp=(x,M)=>{this.end();const{onEnd:V,onSessionEnd:B,resumeAnimation:L}=this.handlers;if(this.dragSnapToOrigin&&L&&L(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const Y=Vr(x.type==="pointercancel"?this.lastMoveEventInfo:Nr(M,this.transformPagePoint),this.history);this.startEvent&&V&&V(x,Y),B&&B(x,Y)},!Ec(l))return;this.dragSnapToOrigin=d,this.handlers=u,this.transformPagePoint=o,this.contextWindow=c||window;const h=Al(l),m=Nr(h,this.transformPagePoint),{point:g}=m,{timestamp:y}=$t;this.history=[{...g,timestamp:y}];const{onSessionStart:v}=u;v&&v(l,Vr(m,this.history)),this.removeListeners=xl(cl(this.contextWindow,"pointermove",this.handlePointerMove),cl(this.contextWindow,"pointerup",this.handlePointerUp),cl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(l){this.handlers=l}end(){this.removeListeners&&this.removeListeners(),Hn(this.updatePoint)}}function Nr(i,l){return l?{point:l(i.point)}:i}function by(i,l){return{x:i.x-l.x,y:i.y-l.y}}function Vr({point:i},l){return{point:i,delta:by(i,b0(l)),offset:by(i,CS(l)),velocity:jS(l,.1)}}function CS(i){return i[0]}function b0(i){return i[i.length-1]}function jS(i,l){if(i.length<2)return{x:0,y:0};let u=i.length-1,o=null;const c=b0(i);for(;u>=0&&(o=i[u],!(c.timestamp-o.timestamp>Qe(l)));)u--;if(!o)return{x:0,y:0};const d=Ke(c.timestamp-o.timestamp);if(d===0)return{x:0,y:0};const h={x:(c.x-o.x)/d,y:(c.y-o.y)/d};return h.x===1/0&&(h.x=0),h.y===1/0&&(h.y=0),h}function zS(i,{min:l,max:u},o){return l!==void 0&&i<l?i=o?Rt(l,i,o.min):Math.max(i,l):u!==void 0&&i>u&&(i=o?Rt(u,i,o.max):Math.min(i,u)),i}function xy(i,l,u){return{min:l!==void 0?i.min+l:void 0,max:u!==void 0?i.max+u-(i.max-i.min):void 0}}function _S(i,{top:l,left:u,bottom:o,right:c}){return{x:xy(i.x,u,c),y:xy(i.y,l,o)}}function Sy(i,l){let u=l.min-i.min,o=l.max-i.max;return l.max-l.min<i.max-i.min&&([u,o]=[o,u]),{min:u,max:o}}function wS(i,l){return{x:Sy(i.x,l.x),y:Sy(i.y,l.y)}}function US(i,l){let u=.5;const o=le(i),c=le(l);return c>o?u=dl(l.min,l.max-o,i.min):o>c&&(u=dl(i.min,i.max-c,l.min)),fn(0,1,u)}function BS(i,l){const u={};return l.min!==void 0&&(u.min=l.min-i.min),l.max!==void 0&&(u.max=l.max-i.min),u}const Ir=.35;function LS(i=Ir){return i===!1?i=0:i===!0&&(i=Ir),{x:Ty(i,"left","right"),y:Ty(i,"top","bottom")}}function Ty(i,l,u){return{min:Ay(i,l),max:Ay(i,u)}}function Ay(i,l){return typeof i=="number"?i:i[l]||0}const HS=new WeakMap;class qS{constructor(l){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=_t(),this.visualElement=l}start(l,{snapToCursor:u=!1}={}){const{presenceContext:o}=this.visualElement;if(o&&o.isPresent===!1)return;const c=v=>{const{dragSnapToOrigin:x}=this.getProps();x?this.pauseAnimation():this.stopAnimation(),u&&this.snapToCursor(Al(v).point)},d=(v,x)=>{const{drag:M,dragPropagation:V,onDragStart:B}=this.getProps();if(M&&!V&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ax(M),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),je(Y=>{let G=this.getAxisMotionValue(Y).get()||0;if(ke.test(G)){const{projection:J}=this.visualElement;if(J&&J.layout){const H=J.layout.layoutBox[Y];H&&(G=le(H)*(parseFloat(G)/100))}}this.originPoint[Y]=G}),B&&Ot.postRender(()=>B(v,x)),Pr(this.visualElement,"transform");const{animationState:L}=this.visualElement;L&&L.setActive("whileDrag",!0)},h=(v,x)=>{const{dragPropagation:M,dragDirectionLock:V,onDirectionLock:B,onDrag:L}=this.getProps();if(!M&&!this.openDragLock)return;const{offset:Y}=x;if(V&&this.currentDirection===null){this.currentDirection=YS(Y),this.currentDirection!==null&&B&&B(this.currentDirection);return}this.updateAxis("x",x.point,Y),this.updateAxis("y",x.point,Y),this.visualElement.render(),L&&L(v,x)},m=(v,x)=>this.stop(v,x),g=()=>je(v=>{var x;return this.getAnimationState(v)==="paused"&&((x=this.getAxisMotionValue(v).animation)==null?void 0:x.play())}),{dragSnapToOrigin:y}=this.getProps();this.panSession=new v0(l,{onSessionStart:c,onStart:d,onMove:h,onSessionEnd:m,resumeAnimation:g},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:y,contextWindow:g0(this.visualElement)})}stop(l,u){const o=this.isDragging;if(this.cancel(),!o)return;const{velocity:c}=u;this.startAnimation(c);const{onDragEnd:d}=this.getProps();d&&Ot.postRender(()=>d(l,u))}cancel(){this.isDragging=!1;const{projection:l,animationState:u}=this.visualElement;l&&(l.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:o}=this.getProps();!o&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),u&&u.setActive("whileDrag",!1)}updateAxis(l,u,o){const{drag:c}=this.getProps();if(!o||!Ys(l,c,this.currentDirection))return;const d=this.getAxisMotionValue(l);let h=this.originPoint[l]+o[l];this.constraints&&this.constraints[l]&&(h=zS(h,this.constraints[l],this.elastic[l])),d.set(h)}resolveConstraints(){var d;const{dragConstraints:l,dragElastic:u}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(d=this.visualElement.projection)==null?void 0:d.layout,c=this.constraints;l&&$a(l)?this.constraints||(this.constraints=this.resolveRefConstraints()):l&&o?this.constraints=_S(o.layoutBox,l):this.constraints=!1,this.elastic=LS(u),c!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&je(h=>{this.constraints!==!1&&this.getAxisMotionValue(h)&&(this.constraints[h]=BS(o.layoutBox[h],this.constraints[h]))})}resolveRefConstraints(){const{dragConstraints:l,onMeasureDragConstraints:u}=this.getProps();if(!l||!$a(l))return!1;const o=l.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=NS(o,c.root,this.visualElement.getTransformPagePoint());let h=wS(c.layout.layoutBox,d);if(u){const m=u(xS(h));this.hasMutatedConstraints=!!m,m&&(h=f0(m))}return h}startAnimation(l){const{drag:u,dragMomentum:o,dragElastic:c,dragTransition:d,dragSnapToOrigin:h,onDragTransitionEnd:m}=this.getProps(),g=this.constraints||{},y=je(v=>{if(!Ys(v,u,this.currentDirection))return;let x=g&&g[v]||{};h&&(x={min:0,max:0});const M=c?200:1e6,V=c?40:1e7,B={type:"inertia",velocity:o?l[v]:0,bounceStiffness:M,bounceDamping:V,timeConstant:750,restDelta:1,restSpeed:10,...d,...x};return this.startAxisValueAnimation(v,B)});return Promise.all(y).then(m)}startAxisValueAnimation(l,u){const o=this.getAxisMotionValue(l);return Pr(this.visualElement,l),o.start(_c(l,o,0,u,this.visualElement,!1))}stopAnimation(){je(l=>this.getAxisMotionValue(l).stop())}pauseAnimation(){je(l=>{var u;return(u=this.getAxisMotionValue(l).animation)==null?void 0:u.pause()})}getAnimationState(l){var u;return(u=this.getAxisMotionValue(l).animation)==null?void 0:u.state}getAxisMotionValue(l){const u=`_drag${l.toUpperCase()}`,o=this.visualElement.getProps(),c=o[u];return c||this.visualElement.getValue(l,(o.initial?o.initial[l]:void 0)||0)}snapToCursor(l){je(u=>{const{drag:o}=this.getProps();if(!Ys(u,o,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(u);if(c&&c.layout){const{min:h,max:m}=c.layout.layoutBox[u];d.set(l[u]-Rt(h,m,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:l,dragConstraints:u}=this.getProps(),{projection:o}=this.visualElement;if(!$a(u)||!o||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};je(h=>{const m=this.getAxisMotionValue(h);if(m&&this.constraints!==!1){const g=m.get();c[h]=US({min:g,max:g},this.constraints[h])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",o.root&&o.root.updateScroll(),o.updateLayout(),this.resolveConstraints(),je(h=>{if(!Ys(h,l,null))return;const m=this.getAxisMotionValue(h),{min:g,max:y}=this.constraints[h];m.set(Rt(g,y,c[h]))})}addListeners(){if(!this.visualElement.current)return;HS.set(this.visualElement,this);const l=this.visualElement.current,u=cl(l,"pointerdown",g=>{const{drag:y,dragListener:v=!0}=this.getProps();y&&v&&this.start(g)}),o=()=>{const{dragConstraints:g}=this.getProps();$a(g)&&g.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",o);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Ot.read(o);const h=bl(window,"resize",()=>this.scalePositionWithinConstraints()),m=c.addEventListener("didUpdate",({delta:g,hasLayoutChanged:y})=>{this.isDragging&&y&&(je(v=>{const x=this.getAxisMotionValue(v);x&&(this.originPoint[v]+=g[v].translate,x.set(x.get()+g[v].translate))}),this.visualElement.render())});return()=>{h(),u(),d(),m&&m()}}getProps(){const l=this.visualElement.getProps(),{drag:u=!1,dragDirectionLock:o=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:h=Ir,dragMomentum:m=!0}=l;return{...l,drag:u,dragDirectionLock:o,dragPropagation:c,dragConstraints:d,dragElastic:h,dragMomentum:m}}}function Ys(i,l,u){return(l===!0||l===i)&&(u===null||u===i)}function YS(i,l=10){let u=null;return Math.abs(i.y)>l?u="y":Math.abs(i.x)>l&&(u="x"),u}class GS extends Yn{constructor(l){super(l),this.removeGroupControls=ze,this.removeListeners=ze,this.controls=new qS(l)}mount(){const{dragControls:l}=this.node.getProps();l&&(this.removeGroupControls=l.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ze}unmount(){this.removeGroupControls(),this.removeListeners()}}const My=i=>(l,u)=>{i&&Ot.postRender(()=>i(l,u))};class XS extends Yn{constructor(){super(...arguments),this.removePointerDownListener=ze}onPointerDown(l){this.session=new v0(l,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:g0(this.node)})}createPanHandlers(){const{onPanSessionStart:l,onPanStart:u,onPan:o,onPanEnd:c}=this.node.getProps();return{onSessionStart:My(l),onStart:My(u),onMove:o,onEnd:(d,h)=>{delete this.session,c&&Ot.postRender(()=>c(d,h))}}}mount(){this.removePointerDownListener=cl(this.node.current,"pointerdown",l=>this.onPointerDown(l))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Qs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ey(i,l){return l.max===l.min?0:i/(l.max-l.min)*100}const sl={correct:(i,l)=>{if(!l.target)return i;if(typeof i=="string")if(tt.test(i))i=parseFloat(i);else return i;const u=Ey(i,l.target.x),o=Ey(i,l.target.y);return`${u}% ${o}%`}},ZS={correct:(i,{treeScale:l,projectionDelta:u})=>{const o=i,c=qn.parse(i);if(c.length>5)return o;const d=qn.createTransformer(i),h=typeof c[0]!="number"?1:0,m=u.x.scale*l.x,g=u.y.scale*l.y;c[0+h]/=m,c[1+h]/=g;const y=Rt(m,g,.5);return typeof c[2+h]=="number"&&(c[2+h]/=y),typeof c[3+h]=="number"&&(c[3+h]/=y),d(c)}};class QS extends et.Component{componentDidMount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:o,layoutId:c}=this.props,{projection:d}=l;Nx(KS),d&&(u.group&&u.group.add(d),o&&o.register&&c&&o.register(d),d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),Qs.hasEverUpdated=!0}getSnapshotBeforeUpdate(l){const{layoutDependency:u,visualElement:o,drag:c,isPresent:d}=this.props,{projection:h}=o;return h&&(h.isPresent=d,c||l.layoutDependency!==u||u===void 0||l.isPresent!==d?h.willUpdate():this.safeToRemove(),l.isPresent!==d&&(d?h.promote():h.relegate()||Ot.postRender(()=>{const m=h.getStack();(!m||!m.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:l}=this.props.visualElement;l&&(l.root.didUpdate(),Mc.postRender(()=>{!l.currentAnimation&&l.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:o}=this.props,{projection:c}=l;c&&(c.scheduleCheckAfterUnmount(),u&&u.group&&u.group.remove(c),o&&o.deregister&&o.deregister(c))}safeToRemove(){const{safeToRemove:l}=this.props;l&&l()}render(){return null}}function x0(i){const[l,u]=hx(),o=et.useContext(np);return R.jsx(QS,{...i,layoutGroup:o,switchLayoutGroup:et.useContext($p),isPresent:l,safeToRemove:u})}const KS={borderRadius:{...sl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sl,borderTopRightRadius:sl,borderBottomLeftRadius:sl,borderBottomRightRadius:sl,boxShadow:ZS};function kS(i,l,u){const o=ae(i)?i:ni(i);return o.start(_c("",o,l,u)),o.animation}const JS=(i,l)=>i.depth-l.depth;class PS{constructor(){this.children=[],this.isDirty=!1}add(l){lc(this.children,l),this.isDirty=!0}remove(l){sc(this.children,l),this.isDirty=!0}forEach(l){this.isDirty&&this.children.sort(JS),this.isDirty=!1,this.children.forEach(l)}}function FS(i,l){const u=fe.now(),o=({timestamp:c})=>{const d=c-u;d>=l&&(Hn(o),i(d-l))};return Ot.setup(o,!0),()=>Hn(o)}const S0=["TopLeft","TopRight","BottomLeft","BottomRight"],WS=S0.length,Dy=i=>typeof i=="string"?parseFloat(i):i,Ry=i=>typeof i=="number"||tt.test(i);function $S(i,l,u,o,c,d){c?(i.opacity=Rt(0,u.opacity??1,IS(o)),i.opacityExit=Rt(l.opacity??1,0,t2(o))):d&&(i.opacity=Rt(l.opacity??1,u.opacity??1,o));for(let h=0;h<WS;h++){const m=`border${S0[h]}Radius`;let g=Oy(l,m),y=Oy(u,m);if(g===void 0&&y===void 0)continue;g||(g=0),y||(y=0),g===0||y===0||Ry(g)===Ry(y)?(i[m]=Math.max(Rt(Dy(g),Dy(y),o),0),(ke.test(y)||ke.test(g))&&(i[m]+="%")):i[m]=y}(l.rotate||u.rotate)&&(i.rotate=Rt(l.rotate||0,u.rotate||0,o))}function Oy(i,l){return i[l]!==void 0?i[l]:i.borderRadius}const IS=T0(0,.5,dp),t2=T0(.5,.95,ze);function T0(i,l,u){return o=>o<i?0:o>l?1:u(dl(i,l,o))}function Ny(i,l){i.min=l.min,i.max=l.max}function Ce(i,l){Ny(i.x,l.x),Ny(i.y,l.y)}function Vy(i,l){i.translate=l.translate,i.scale=l.scale,i.originPoint=l.originPoint,i.origin=l.origin}function Cy(i,l,u,o,c){return i-=l,i=Fs(i,1/u,o),c!==void 0&&(i=Fs(i,1/c,o)),i}function e2(i,l=0,u=1,o=.5,c,d=i,h=i){if(ke.test(l)&&(l=parseFloat(l),l=Rt(h.min,h.max,l/100)-h.min),typeof l!="number")return;let m=Rt(d.min,d.max,o);i===d&&(m-=l),i.min=Cy(i.min,l,u,m,c),i.max=Cy(i.max,l,u,m,c)}function jy(i,l,[u,o,c],d,h){e2(i,l[u],l[o],l[c],l.scale,d,h)}const n2=["x","scaleX","originX"],a2=["y","scaleY","originY"];function zy(i,l,u,o){jy(i.x,l,n2,u?u.x:void 0,o?o.x:void 0),jy(i.y,l,a2,u?u.y:void 0,o?o.y:void 0)}function _y(i){return i.translate===0&&i.scale===1}function A0(i){return _y(i.x)&&_y(i.y)}function wy(i,l){return i.min===l.min&&i.max===l.max}function i2(i,l){return wy(i.x,l.x)&&wy(i.y,l.y)}function Uy(i,l){return Math.round(i.min)===Math.round(l.min)&&Math.round(i.max)===Math.round(l.max)}function M0(i,l){return Uy(i.x,l.x)&&Uy(i.y,l.y)}function By(i){return le(i.x)/le(i.y)}function Ly(i,l){return i.translate===l.translate&&i.scale===l.scale&&i.originPoint===l.originPoint}class l2{constructor(){this.members=[]}add(l){lc(this.members,l),l.scheduleRender()}remove(l){if(sc(this.members,l),l===this.prevLead&&(this.prevLead=void 0),l===this.lead){const u=this.members[this.members.length-1];u&&this.promote(u)}}relegate(l){const u=this.members.findIndex(c=>l===c);if(u===0)return!1;let o;for(let c=u;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){o=d;break}}return o?(this.promote(o),!0):!1}promote(l,u){const o=this.lead;if(l!==o&&(this.prevLead=o,this.lead=l,l.show(),o)){o.instance&&o.scheduleRender(),l.scheduleRender(),l.resumeFrom=o,u&&(l.resumeFrom.preserveOpacity=!0),o.snapshot&&(l.snapshot=o.snapshot,l.snapshot.latestValues=o.animationValues||o.latestValues),l.root&&l.root.isUpdating&&(l.isLayoutDirty=!0);const{crossfade:c}=l.options;c===!1&&o.hide()}}exitAnimationComplete(){this.members.forEach(l=>{const{options:u,resumingFrom:o}=l;u.onExitComplete&&u.onExitComplete(),o&&o.options.onExitComplete&&o.options.onExitComplete()})}scheduleRender(){this.members.forEach(l=>{l.instance&&l.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function s2(i,l,u){let o="";const c=i.x.translate/l.x,d=i.y.translate/l.y,h=(u==null?void 0:u.z)||0;if((c||d||h)&&(o=`translate3d(${c}px, ${d}px, ${h}px) `),(l.x!==1||l.y!==1)&&(o+=`scale(${1/l.x}, ${1/l.y}) `),u){const{transformPerspective:y,rotate:v,rotateX:x,rotateY:M,skewX:V,skewY:B}=u;y&&(o=`perspective(${y}px) ${o}`),v&&(o+=`rotate(${v}deg) `),x&&(o+=`rotateX(${x}deg) `),M&&(o+=`rotateY(${M}deg) `),V&&(o+=`skewX(${V}deg) `),B&&(o+=`skewY(${B}deg) `)}const m=i.x.scale*l.x,g=i.y.scale*l.y;return(m!==1||g!==1)&&(o+=`scale(${m}, ${g})`),o||"none"}const Cr=["","X","Y","Z"],u2={visibility:"hidden"},o2=1e3;let r2=0;function jr(i,l,u,o){const{latestValues:c}=l;c[i]&&(u[i]=c[i],l.setStaticValue(i,0),o&&(o[i]=0))}function E0(i){if(i.hasCheckedOptimisedAppear=!0,i.root===i)return;const{visualElement:l}=i.options;if(!l)return;const u=u0(l);if(window.MotionHasOptimisedAnimation(u,"transform")){const{layout:c,layoutId:d}=i.options;window.MotionCancelOptimisedAnimation(u,"transform",Ot,!(c||d))}const{parent:o}=i;o&&!o.hasCheckedOptimisedAppear&&E0(o)}function D0({attachResizeListener:i,defaultParent:l,measureScroll:u,checkIsScrollRoot:o,resetTransform:c}){return class{constructor(h={},m=l==null?void 0:l()){this.id=r2++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(h2),this.nodes.forEach(g2),this.nodes.forEach(v2),this.nodes.forEach(d2)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=h,this.root=m?m.root||m:this,this.path=m?[...m.path,m]:[],this.parent=m,this.depth=m?m.depth+1:0;for(let g=0;g<this.path.length;g++)this.path[g].shouldResetTransform=!0;this.root===this&&(this.nodes=new PS)}addEventListener(h,m){return this.eventHandlers.has(h)||this.eventHandlers.set(h,new rc),this.eventHandlers.get(h).add(m)}notifyListeners(h,...m){const g=this.eventHandlers.get(h);g&&g.notify(...m)}hasListeners(h){return this.eventHandlers.has(h)}mount(h){if(this.instance)return;this.isSVG=Kp(h)&&!rx(h),this.instance=h;const{layoutId:m,layout:g,visualElement:y}=this.options;if(y&&!y.current&&y.mount(h),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(g||m)&&(this.isLayoutDirty=!0),i){let v;const x=()=>this.root.updateBlockedByResize=!1;i(h,()=>{this.root.updateBlockedByResize=!0,v&&v(),v=FS(x,250),Qs.hasAnimatedSinceResize&&(Qs.hasAnimatedSinceResize=!1,this.nodes.forEach(qy))})}m&&this.root.registerSharedNode(m,this),this.options.animate!==!1&&y&&(m||g)&&this.addEventListener("didUpdate",({delta:v,hasLayoutChanged:x,hasRelativeLayoutChanged:M,layout:V})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const B=this.options.transition||y.getDefaultTransition()||A2,{onLayoutAnimationStart:L,onLayoutAnimationComplete:Y}=y.getProps(),G=!this.targetLayout||!M0(this.targetLayout,V),J=!x&&M;if(this.options.layoutRoot||this.resumeFrom||J||x&&(G||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const H={...Tc(B,"layout"),onPlay:L,onComplete:Y};(y.shouldReduceMotion||this.options.layoutRoot)&&(H.delay=0,H.type=!1),this.startAnimation(H),this.setAnimationOrigin(v,J)}else x||qy(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=V})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const h=this.getStack();h&&h.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Hn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(b2),this.animationId++)}getTransformTemplate(){const{visualElement:h}=this.options;return h&&h.getProps().transformTemplate}willUpdate(h=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&E0(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const x=this.path[v];x.shouldResetTransform=!0,x.updateScroll("snapshot"),x.options.layoutRoot&&x.willUpdate(!1)}const{layoutId:m,layout:g}=this.options;if(m===void 0&&!g)return;const y=this.getTransformTemplate();this.prevTransformTemplateValue=y?y(this.latestValues,""):void 0,this.updateSnapshot(),h&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Hy);return}this.isUpdating||this.nodes.forEach(y2),this.isUpdating=!1,this.nodes.forEach(p2),this.nodes.forEach(c2),this.nodes.forEach(f2),this.clearAllSnapshots();const m=fe.now();$t.delta=fn(0,1e3/60,m-$t.timestamp),$t.timestamp=m,$t.isProcessing=!0,Tr.update.process($t),Tr.preRender.process($t),Tr.render.process($t),$t.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Mc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(m2),this.sharedNodes.forEach(x2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ot.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ot.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!le(this.snapshot.measuredBox.x)&&!le(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let g=0;g<this.path.length;g++)this.path[g].updateScroll();const h=this.layout;this.layout=this.measure(!1),this.layoutCorrected=_t(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:m}=this.options;m&&m.notify("LayoutMeasure",this.layout.layoutBox,h?h.layoutBox:void 0)}updateScroll(h="measure"){let m=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===h&&(m=!1),m&&this.instance){const g=o(this.instance);this.scroll={animationId:this.root.animationId,phase:h,isRoot:g,offset:u(this.instance),wasRoot:this.scroll?this.scroll.isRoot:g}}}resetTransform(){if(!c)return;const h=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,m=this.projectionDelta&&!A0(this.projectionDelta),g=this.getTransformTemplate(),y=g?g(this.latestValues,""):void 0,v=y!==this.prevTransformTemplateValue;h&&this.instance&&(m||ua(this.latestValues)||v)&&(c(this.instance,y),this.shouldResetTransform=!1,this.scheduleRender())}measure(h=!0){const m=this.measurePageBox();let g=this.removeElementScroll(m);return h&&(g=this.removeTransform(g)),M2(g),{animationId:this.root.animationId,measuredBox:m,layoutBox:g,latestValues:{},source:this.id}}measurePageBox(){var y;const{visualElement:h}=this.options;if(!h)return _t();const m=h.measureViewportBox();if(!(((y=this.scroll)==null?void 0:y.wasRoot)||this.path.some(E2))){const{scroll:v}=this.root;v&&(ti(m.x,v.offset.x),ti(m.y,v.offset.y))}return m}removeElementScroll(h){var g;const m=_t();if(Ce(m,h),(g=this.scroll)!=null&&g.wasRoot)return m;for(let y=0;y<this.path.length;y++){const v=this.path[y],{scroll:x,options:M}=v;v!==this.root&&x&&M.layoutScroll&&(x.wasRoot&&Ce(m,h),ti(m.x,x.offset.x),ti(m.y,x.offset.y))}return m}applyTransform(h,m=!1){const g=_t();Ce(g,h);for(let y=0;y<this.path.length;y++){const v=this.path[y];!m&&v.options.layoutScroll&&v.scroll&&v!==v.root&&ei(g,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),ua(v.latestValues)&&ei(g,v.latestValues)}return ua(this.latestValues)&&ei(g,this.latestValues),g}removeTransform(h){const m=_t();Ce(m,h);for(let g=0;g<this.path.length;g++){const y=this.path[g];if(!y.instance||!ua(y.latestValues))continue;Wr(y.latestValues)&&y.updateSnapshot();const v=_t(),x=y.measurePageBox();Ce(v,x),zy(m,y.latestValues,y.snapshot?y.snapshot.layoutBox:void 0,v)}return ua(this.latestValues)&&zy(m,this.latestValues),m}setTargetDelta(h){this.targetDelta=h,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(h){this.options={...this.options,...h,crossfade:h.crossfade!==void 0?h.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==$t.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(h=!1){var M;const m=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=m.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=m.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=m.isSharedProjectionDirty);const g=!!this.resumingFrom||this!==m;if(!(h||g&&this.isSharedProjectionDirty||this.isProjectionDirty||(M=this.parent)!=null&&M.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:v,layoutId:x}=this.options;if(!(!this.layout||!(v||x))){if(this.resolvedRelativeTargetAt=$t.timestamp,!this.targetDelta&&!this.relativeTarget){const V=this.getClosestProjectingParent();V&&V.layout&&this.animationProgress!==1?(this.relativeParent=V,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_t(),this.relativeTargetOrigin=_t(),hl(this.relativeTargetOrigin,this.layout.layoutBox,V.layout.layoutBox),Ce(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=_t(),this.targetWithTransforms=_t()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),RS(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ce(this.target,this.layout.layoutBox),y0(this.target,this.targetDelta)):Ce(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const V=this.getClosestProjectingParent();V&&!!V.resumingFrom==!!this.resumingFrom&&!V.options.layoutScroll&&V.target&&this.animationProgress!==1?(this.relativeParent=V,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_t(),this.relativeTargetOrigin=_t(),hl(this.relativeTargetOrigin,this.target,V.target),Ce(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Wr(this.parent.latestValues)||m0(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var B;const h=this.getLead(),m=!!this.resumingFrom||this!==h;let g=!0;if((this.isProjectionDirty||(B=this.parent)!=null&&B.isProjectionDirty)&&(g=!1),m&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(g=!1),this.resolvedRelativeTargetAt===$t.timestamp&&(g=!1),g)return;const{layout:y,layoutId:v}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(y||v))return;Ce(this.layoutCorrected,this.layout.layoutBox);const x=this.treeScale.x,M=this.treeScale.y;OS(this.layoutCorrected,this.treeScale,this.path,m),h.layout&&!h.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(h.target=h.layout.layoutBox,h.targetWithTransforms=_t());const{target:V}=h;if(!V){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Vy(this.prevProjectionDelta.x,this.projectionDelta.x),Vy(this.prevProjectionDelta.y,this.projectionDelta.y)),fl(this.projectionDelta,this.layoutCorrected,V,this.latestValues),(this.treeScale.x!==x||this.treeScale.y!==M||!Ly(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ly(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",V))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(h=!0){var m;if((m=this.options.visualElement)==null||m.scheduleRender(),h){const g=this.getStack();g&&g.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Ia(),this.projectionDelta=Ia(),this.projectionDeltaWithTransform=Ia()}setAnimationOrigin(h,m=!1){const g=this.snapshot,y=g?g.latestValues:{},v={...this.latestValues},x=Ia();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!m;const M=_t(),V=g?g.source:void 0,B=this.layout?this.layout.source:void 0,L=V!==B,Y=this.getStack(),G=!Y||Y.members.length<=1,J=!!(L&&!G&&this.options.crossfade===!0&&!this.path.some(T2));this.animationProgress=0;let H;this.mixTargetDelta=it=>{const q=it/1e3;Yy(x.x,h.x,q),Yy(x.y,h.y,q),this.setTargetDelta(x),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(hl(M,this.layout.layoutBox,this.relativeParent.layout.layoutBox),S2(this.relativeTarget,this.relativeTargetOrigin,M,q),H&&i2(this.relativeTarget,H)&&(this.isProjectionDirty=!1),H||(H=_t()),Ce(H,this.relativeTarget)),L&&(this.animationValues=v,$S(v,y,this.latestValues,q,J,G)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=q},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(h){var m,g,y;this.notifyListeners("animationStart"),(m=this.currentAnimation)==null||m.stop(),(y=(g=this.resumingFrom)==null?void 0:g.currentAnimation)==null||y.stop(),this.pendingAnimation&&(Hn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ot.update(()=>{Qs.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ni(0)),this.currentAnimation=kS(this.motionValue,[0,1e3],{...h,velocity:0,isSync:!0,onUpdate:v=>{this.mixTargetDelta(v),h.onUpdate&&h.onUpdate(v)},onStop:()=>{},onComplete:()=>{h.onComplete&&h.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const h=this.getStack();h&&h.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(o2),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const h=this.getLead();let{targetWithTransforms:m,target:g,layout:y,latestValues:v}=h;if(!(!m||!g||!y)){if(this!==h&&this.layout&&y&&R0(this.options.animationType,this.layout.layoutBox,y.layoutBox)){g=this.target||_t();const x=le(this.layout.layoutBox.x);g.x.min=h.target.x.min,g.x.max=g.x.min+x;const M=le(this.layout.layoutBox.y);g.y.min=h.target.y.min,g.y.max=g.y.min+M}Ce(m,g),ei(m,v),fl(this.projectionDeltaWithTransform,this.layoutCorrected,m,v)}}registerSharedNode(h,m){this.sharedNodes.has(h)||this.sharedNodes.set(h,new l2),this.sharedNodes.get(h).add(m);const y=m.options.initialPromotionConfig;m.promote({transition:y?y.transition:void 0,preserveFollowOpacity:y&&y.shouldPreserveFollowOpacity?y.shouldPreserveFollowOpacity(m):void 0})}isLead(){const h=this.getStack();return h?h.lead===this:!0}getLead(){var m;const{layoutId:h}=this.options;return h?((m=this.getStack())==null?void 0:m.lead)||this:this}getPrevLead(){var m;const{layoutId:h}=this.options;return h?(m=this.getStack())==null?void 0:m.prevLead:void 0}getStack(){const{layoutId:h}=this.options;if(h)return this.root.sharedNodes.get(h)}promote({needsReset:h,transition:m,preserveFollowOpacity:g}={}){const y=this.getStack();y&&y.promote(this,g),h&&(this.projectionDelta=void 0,this.needsReset=!0),m&&this.setOptions({transition:m})}relegate(){const h=this.getStack();return h?h.relegate(this):!1}resetSkewAndRotation(){const{visualElement:h}=this.options;if(!h)return;let m=!1;const{latestValues:g}=h;if((g.z||g.rotate||g.rotateX||g.rotateY||g.rotateZ||g.skewX||g.skewY)&&(m=!0),!m)return;const y={};g.z&&jr("z",h,y,this.animationValues);for(let v=0;v<Cr.length;v++)jr(`rotate${Cr[v]}`,h,y,this.animationValues),jr(`skew${Cr[v]}`,h,y,this.animationValues);h.render();for(const v in y)h.setStaticValue(v,y[v]),this.animationValues&&(this.animationValues[v]=y[v]);h.scheduleRender()}getProjectionStyles(h){if(!this.instance||this.isSVG)return;if(!this.isVisible)return u2;const m={visibility:""},g=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,m.opacity="",m.pointerEvents=Zs(h==null?void 0:h.pointerEvents)||"",m.transform=g?g(this.latestValues,""):"none",m;const y=this.getLead();if(!this.projectionDelta||!this.layout||!y.target){const V={};return this.options.layoutId&&(V.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,V.pointerEvents=Zs(h==null?void 0:h.pointerEvents)||""),this.hasProjected&&!ua(this.latestValues)&&(V.transform=g?g({},""):"none",this.hasProjected=!1),V}const v=y.animationValues||y.latestValues;this.applyTransformsToTarget(),m.transform=s2(this.projectionDeltaWithTransform,this.treeScale,v),g&&(m.transform=g(v,m.transform));const{x,y:M}=this.projectionDelta;m.transformOrigin=`${x.origin*100}% ${M.origin*100}% 0`,y.animationValues?m.opacity=y===this?v.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:m.opacity=y===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const V in gl){if(v[V]===void 0)continue;const{correct:B,applyTo:L,isCSSVariable:Y}=gl[V],G=m.transform==="none"?v[V]:B(v[V],y);if(L){const J=L.length;for(let H=0;H<J;H++)m[L[H]]=G}else Y?this.options.visualElement.renderState.vars[V]=G:m[V]=G}return this.options.layoutId&&(m.pointerEvents=y===this?Zs(h==null?void 0:h.pointerEvents)||"":"none"),m}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(h=>{var m;return(m=h.currentAnimation)==null?void 0:m.stop()}),this.root.nodes.forEach(Hy),this.root.sharedNodes.clear()}}}function c2(i){i.updateLayout()}function f2(i){var u;const l=((u=i.resumeFrom)==null?void 0:u.snapshot)||i.snapshot;if(i.isLead()&&i.layout&&l&&i.hasListeners("didUpdate")){const{layoutBox:o,measuredBox:c}=i.layout,{animationType:d}=i.options,h=l.source!==i.layout.source;d==="size"?je(x=>{const M=h?l.measuredBox[x]:l.layoutBox[x],V=le(M);M.min=o[x].min,M.max=M.min+V}):R0(d,l.layoutBox,o)&&je(x=>{const M=h?l.measuredBox[x]:l.layoutBox[x],V=le(o[x]);M.max=M.min+V,i.relativeTarget&&!i.currentAnimation&&(i.isProjectionDirty=!0,i.relativeTarget[x].max=i.relativeTarget[x].min+V)});const m=Ia();fl(m,o,l.layoutBox);const g=Ia();h?fl(g,i.applyTransform(c,!0),l.measuredBox):fl(g,o,l.layoutBox);const y=!A0(m);let v=!1;if(!i.resumeFrom){const x=i.getClosestProjectingParent();if(x&&!x.resumeFrom){const{snapshot:M,layout:V}=x;if(M&&V){const B=_t();hl(B,l.layoutBox,M.layoutBox);const L=_t();hl(L,o,V.layoutBox),M0(B,L)||(v=!0),x.options.layoutRoot&&(i.relativeTarget=L,i.relativeTargetOrigin=B,i.relativeParent=x)}}}i.notifyListeners("didUpdate",{layout:o,snapshot:l,delta:g,layoutDelta:m,hasLayoutChanged:y,hasRelativeLayoutChanged:v})}else if(i.isLead()){const{onExitComplete:o}=i.options;o&&o()}i.options.transition=void 0}function h2(i){i.parent&&(i.isProjecting()||(i.isProjectionDirty=i.parent.isProjectionDirty),i.isSharedProjectionDirty||(i.isSharedProjectionDirty=!!(i.isProjectionDirty||i.parent.isProjectionDirty||i.parent.isSharedProjectionDirty)),i.isTransformDirty||(i.isTransformDirty=i.parent.isTransformDirty))}function d2(i){i.isProjectionDirty=i.isSharedProjectionDirty=i.isTransformDirty=!1}function m2(i){i.clearSnapshot()}function Hy(i){i.clearMeasurements()}function y2(i){i.isLayoutDirty=!1}function p2(i){const{visualElement:l}=i.options;l&&l.getProps().onBeforeLayoutMeasure&&l.notify("BeforeLayoutMeasure"),i.resetTransform()}function qy(i){i.finishAnimation(),i.targetDelta=i.relativeTarget=i.target=void 0,i.isProjectionDirty=!0}function g2(i){i.resolveTargetDelta()}function v2(i){i.calcProjection()}function b2(i){i.resetSkewAndRotation()}function x2(i){i.removeLeadSnapshot()}function Yy(i,l,u){i.translate=Rt(l.translate,0,u),i.scale=Rt(l.scale,1,u),i.origin=l.origin,i.originPoint=l.originPoint}function Gy(i,l,u,o){i.min=Rt(l.min,u.min,o),i.max=Rt(l.max,u.max,o)}function S2(i,l,u,o){Gy(i.x,l.x,u.x,o),Gy(i.y,l.y,u.y,o)}function T2(i){return i.animationValues&&i.animationValues.opacityExit!==void 0}const A2={duration:.45,ease:[.4,0,.1,1]},Xy=i=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(i),Zy=Xy("applewebkit/")&&!Xy("chrome/")?Math.round:ze;function Qy(i){i.min=Zy(i.min),i.max=Zy(i.max)}function M2(i){Qy(i.x),Qy(i.y)}function R0(i,l,u){return i==="position"||i==="preserve-aspect"&&!DS(By(l),By(u),.2)}function E2(i){var l;return i!==i.root&&((l=i.scroll)==null?void 0:l.wasRoot)}const D2=D0({attachResizeListener:(i,l)=>bl(i,"resize",l),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),zr={current:void 0},O0=D0({measureScroll:i=>({x:i.scrollLeft,y:i.scrollTop}),defaultParent:()=>{if(!zr.current){const i=new D2({});i.mount(window),i.setOptions({layoutScroll:!0}),zr.current=i}return zr.current},resetTransform:(i,l)=>{i.style.transform=l!==void 0?l:"none"},checkIsScrollRoot:i=>window.getComputedStyle(i).position==="fixed"}),R2={pan:{Feature:XS},drag:{Feature:GS,ProjectionNode:O0,MeasureLayout:x0}};function Ky(i,l,u){const{props:o}=i;i.animationState&&o.whileHover&&i.animationState.setActive("whileHover",u==="Start");const c="onHover"+u,d=o[c];d&&Ot.postRender(()=>d(l,Al(l)))}class O2 extends Yn{mount(){const{current:l}=this.node;l&&(this.unmount=ix(l,(u,o)=>(Ky(this.node,o,"Start"),c=>Ky(this.node,c,"End"))))}unmount(){}}class N2 extends Yn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let l=!1;try{l=this.node.current.matches(":focus-visible")}catch{l=!0}!l||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=xl(bl(this.node.current,"focus",()=>this.onFocus()),bl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ky(i,l,u){const{props:o}=i;if(i.current instanceof HTMLButtonElement&&i.current.disabled)return;i.animationState&&o.whileTap&&i.animationState.setActive("whileTap",u==="Start");const c="onTap"+(u==="End"?"":u),d=o[c];d&&Ot.postRender(()=>d(l,Al(l)))}class V2 extends Yn{mount(){const{current:l}=this.node;l&&(this.unmount=ox(l,(u,o)=>(ky(this.node,o,"Start"),(c,{success:d})=>ky(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const tc=new WeakMap,_r=new WeakMap,C2=i=>{const l=tc.get(i.target);l&&l(i)},j2=i=>{i.forEach(C2)};function z2({root:i,...l}){const u=i||document;_r.has(u)||_r.set(u,{});const o=_r.get(u),c=JSON.stringify(l);return o[c]||(o[c]=new IntersectionObserver(j2,{root:i,...l})),o[c]}function _2(i,l,u){const o=z2(l);return tc.set(i,u),o.observe(i),()=>{tc.delete(i),o.unobserve(i)}}const w2={some:0,all:1};class U2 extends Yn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:l={}}=this.node.getProps(),{root:u,margin:o,amount:c="some",once:d}=l,h={root:u?u.current:void 0,rootMargin:o,threshold:typeof c=="number"?c:w2[c]},m=g=>{const{isIntersecting:y}=g;if(this.isInView===y||(this.isInView=y,d&&!y&&this.hasEnteredView))return;y&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",y);const{onViewportEnter:v,onViewportLeave:x}=this.node.getProps(),M=y?v:x;M&&M(g)};return _2(this.node.current,h,m)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:l,prevProps:u}=this.node;["amount","margin","root"].some(B2(l,u))&&this.startObserver()}unmount(){}}function B2({viewport:i={}},{viewport:l={}}={}){return u=>i[u]!==l[u]}const L2={inView:{Feature:U2},tap:{Feature:V2},focus:{Feature:N2},hover:{Feature:O2}},H2={layout:{ProjectionNode:O0,MeasureLayout:x0}},ec={current:null},N0={current:!1};function q2(){if(N0.current=!0,!!ac)if(window.matchMedia){const i=window.matchMedia("(prefers-reduced-motion)"),l=()=>ec.current=i.matches;i.addListener(l),l()}else ec.current=!1}const Y2=new WeakMap;function G2(i,l,u){for(const o in l){const c=l[o],d=u[o];if(ae(c))i.addValue(o,c);else if(ae(d))i.addValue(o,ni(c,{owner:i}));else if(d!==c)if(i.hasValue(o)){const h=i.getValue(o);h.liveStyle===!0?h.jump(c):h.hasAnimated||h.set(c)}else{const h=i.getStaticValue(o);i.addValue(o,ni(h!==void 0?h:c,{owner:i}))}}for(const o in u)l[o]===void 0&&i.removeValue(o);return l}const Jy=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class X2{scrapeMotionValuesFromProps(l,u,o){return{}}constructor({parent:l,props:u,presenceContext:o,reducedMotionConfig:c,blockInitialAnimation:d,visualState:h},m={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Sc,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const M=fe.now();this.renderScheduledAt<M&&(this.renderScheduledAt=M,Ot.render(this.render,!1,!0))};const{latestValues:g,renderState:y}=h;this.latestValues=g,this.baseTarget={...g},this.initialValues=u.initial?{...g}:{},this.renderState=y,this.parent=l,this.props=u,this.presenceContext=o,this.depth=l?l.depth+1:0,this.reducedMotionConfig=c,this.options=m,this.blockInitialAnimation=!!d,this.isControllingVariants=Is(u),this.isVariantNode=Fp(u),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(l&&l.current);const{willChange:v,...x}=this.scrapeMotionValuesFromProps(u,{},this);for(const M in x){const V=x[M];g[M]!==void 0&&ae(V)&&V.set(g[M],!1)}}mount(l){this.current=l,Y2.set(l,this),this.projection&&!this.projection.instance&&this.projection.mount(l),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((u,o)=>this.bindToMotionValue(o,u)),N0.current||q2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ec.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Hn(this.notifyUpdate),Hn(this.render),this.valueSubscriptions.forEach(l=>l()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const l in this.events)this.events[l].clear();for(const l in this.features){const u=this.features[l];u&&(u.unmount(),u.isMounted=!1)}this.current=null}bindToMotionValue(l,u){this.valueSubscriptions.has(l)&&this.valueSubscriptions.get(l)();const o=si.has(l);o&&this.onBindTransform&&this.onBindTransform();const c=u.on("change",m=>{this.latestValues[l]=m,this.props.onUpdate&&Ot.preRender(this.notifyUpdate),o&&this.projection&&(this.projection.isTransformDirty=!0)}),d=u.on("renderRequest",this.scheduleRender);let h;window.MotionCheckAppearSync&&(h=window.MotionCheckAppearSync(this,l,u)),this.valueSubscriptions.set(l,()=>{c(),d(),h&&h(),u.owner&&u.stop()})}sortNodePosition(l){return!this.current||!this.sortInstanceNodePosition||this.type!==l.type?0:this.sortInstanceNodePosition(this.current,l.current)}updateFeatures(){let l="animation";for(l in ai){const u=ai[l];if(!u)continue;const{isEnabled:o,Feature:c}=u;if(!this.features[l]&&c&&o(this.props)&&(this.features[l]=new c(this)),this.features[l]){const d=this.features[l];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):_t()}getStaticValue(l){return this.latestValues[l]}setStaticValue(l,u){this.latestValues[l]=u}update(l,u){(l.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=l,this.prevPresenceContext=this.presenceContext,this.presenceContext=u;for(let o=0;o<Jy.length;o++){const c=Jy[o];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,h=l[d];h&&(this.propEventSubscriptions[c]=this.on(c,h))}this.prevMotionValues=G2(this,this.scrapeMotionValuesFromProps(l,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(l){return this.props.variants?this.props.variants[l]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(l){const u=this.getClosestVariantNode();if(u)return u.variantChildren&&u.variantChildren.add(l),()=>u.variantChildren.delete(l)}addValue(l,u){const o=this.values.get(l);u!==o&&(o&&this.removeValue(l),this.bindToMotionValue(l,u),this.values.set(l,u),this.latestValues[l]=u.get())}removeValue(l){this.values.delete(l);const u=this.valueSubscriptions.get(l);u&&(u(),this.valueSubscriptions.delete(l)),delete this.latestValues[l],this.removeValueFromRenderState(l,this.renderState)}hasValue(l){return this.values.has(l)}getValue(l,u){if(this.props.values&&this.props.values[l])return this.props.values[l];let o=this.values.get(l);return o===void 0&&u!==void 0&&(o=ni(u===null?void 0:u,{owner:this}),this.addValue(l,o)),o}readValue(l,u){let o=this.latestValues[l]!==void 0||!this.current?this.latestValues[l]:this.getBaseTargetFromProps(this.props,l)??this.readValueFromInstance(this.current,l,this.options);return o!=null&&(typeof o=="string"&&(ap(o)||lp(o))?o=parseFloat(o):!fx(o)&&qn.test(u)&&(o=Yp(l,u)),this.setBaseTarget(l,ae(o)?o.get():o)),ae(o)?o.get():o}setBaseTarget(l,u){this.baseTarget[l]=u}getBaseTarget(l){var d;const{initial:u}=this.props;let o;if(typeof u=="string"||typeof u=="object"){const h=jc(this.props,u,(d=this.presenceContext)==null?void 0:d.custom);h&&(o=h[l])}if(u&&o!==void 0)return o;const c=this.getBaseTargetFromProps(this.props,l);return c!==void 0&&!ae(c)?c:this.initialValues[l]!==void 0&&o===void 0?void 0:this.baseTarget[l]}on(l,u){return this.events[l]||(this.events[l]=new rc),this.events[l].add(u)}notify(l,...u){this.events[l]&&this.events[l].notify(...u)}}class V0 extends X2{constructor(){super(...arguments),this.KeyframeResolver=Ib}sortInstanceNodePosition(l,u){return l.compareDocumentPosition(u)&2?1:-1}getBaseTargetFromProps(l,u){return l.style?l.style[u]:void 0}removeValueFromRenderState(l,{vars:u,style:o}){delete u[l],delete o[l]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:l}=this.props;ae(l)&&(this.childSubscription=l.on("change",u=>{this.current&&(this.current.textContent=`${u}`)}))}}function C0(i,{style:l,vars:u},o,c){Object.assign(i.style,l,c&&c.getProjectionStyles(o));for(const d in u)i.style.setProperty(d,u[d])}function Z2(i){return window.getComputedStyle(i)}class Q2 extends V0{constructor(){super(...arguments),this.type="html",this.renderInstance=C0}readValueFromInstance(l,u){var o;if(si.has(u))return(o=this.projection)!=null&&o.isProjecting?Gr(u):vb(l,u);{const c=Z2(l),d=(hc(u)?c.getPropertyValue(u):c[u])||0;return typeof d=="string"?d.trim():d}}measureInstanceViewportBox(l,{transformPagePoint:u}){return p0(l,u)}build(l,u,o){Nc(l,u,o.transformTemplate)}scrapeMotionValuesFromProps(l,u,o){return zc(l,u,o)}}const j0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function K2(i,l,u,o){C0(i,l,void 0,o);for(const c in l.attrs)i.setAttribute(j0.has(c)?c:Oc(c),l.attrs[c])}class k2 extends V0{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=_t}getBaseTargetFromProps(l,u){return l[u]}readValueFromInstance(l,u){if(si.has(u)){const o=qp(u);return o&&o.default||0}return u=j0.has(u)?u:Oc(u),l.getAttribute(u)}scrapeMotionValuesFromProps(l,u,o){return s0(l,u,o)}build(l,u,o){n0(l,u,this.isSVGTag,o.transformTemplate,o.style)}renderInstance(l,u,o,c){K2(l,u,o,c)}mount(l){this.isSVGTag=i0(l.tagName),super.mount(l)}}const J2=(i,l)=>Cc(i)?new k2(l):new Q2(l,{allowProjection:i!==et.Fragment}),P2=Kx({...vS,...L2,...R2,...H2},J2),Gt=gx(P2);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F2=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),W2=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(l,u,o)=>o?o.toUpperCase():u.toLowerCase()),Py=i=>{const l=W2(i);return l.charAt(0).toUpperCase()+l.slice(1)},z0=(...i)=>i.filter((l,u,o)=>!!l&&l.trim()!==""&&o.indexOf(l)===u).join(" ").trim(),$2=i=>{for(const l in i)if(l.startsWith("aria-")||l==="role"||l==="title")return!0};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var I2={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tT=et.forwardRef(({color:i="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:h,...m},g)=>et.createElement("svg",{ref:g,...I2,width:l,height:l,stroke:i,strokeWidth:o?Number(u)*24/Number(l):u,className:z0("lucide",c),...!d&&!$2(m)&&{"aria-hidden":"true"},...m},[...h.map(([y,v])=>et.createElement(y,v)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=(i,l)=>{const u=et.forwardRef(({className:o,...c},d)=>et.createElement(tT,{ref:d,iconNode:l,className:z0(`lucide-${F2(Py(i))}`,`lucide-${i}`,o),...c}));return u.displayName=Py(i),u};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eT=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],wr=kt("arrow-right",eT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nT=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],Fy=kt("brain",nT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aT=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],iT=kt("chart-column",aT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lT=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Wy=kt("check",lT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sT=[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]],uT=kt("code",sT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oT=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],rT=kt("file-text",oT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cT=[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]],fT=kt("folder-open",cT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hT=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],dT=kt("github",hT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mT=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],yT=kt("globe",mT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pT=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]],gT=kt("history",pT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vT=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],bT=kt("lightbulb",vT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xT=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],$y=kt("list",xT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ST=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],Iy=kt("message-square",ST);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TT=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],AT=kt("monitor",TT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MT=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],tp=kt("search",MT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ET=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],DT=kt("settings",ET);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RT=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],OT=kt("twitter",RT);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NT=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],VT=kt("upload",NT);function CT(){return R.jsxs("div",{className:"min-h-screen bg-gradient-dark",children:[R.jsx("section",{className:"section-padding min-h-screen flex items-center",children:R.jsxs("div",{className:"max-w-7xl mx-auto",children:[R.jsxs(Gt.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[R.jsx("h1",{className:"text-5xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-soft-white via-electric-blue to-lime-green bg-clip-text text-transparent",children:"Tired of Paying for Tools Like Manus?"}),R.jsx("h2",{className:"text-3xl lg:text-5xl font-mono font-semibold mb-8 text-electric-blue",children:"Meet Lemix – Your Autonomous AI Agent. Free to Start."}),R.jsx("p",{className:"text-xl lg:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed",children:"Lemix handles complex tasks, automates workflows, and gives you full control over powerful AI – all from one sleek interface."}),R.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center items-center",children:[R.jsxs(Gt.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"glow-button text-white text-lg px-8 py-4 flex items-center gap-3",children:["Get Started Free",R.jsx(wr,{className:"w-5 h-5"})]}),R.jsx(Gt.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"glass-card px-8 py-4 text-lg font-medium text-soft-white hover:text-electric-blue transition-colors duration-300",children:"See It in Action"})]})]}),R.jsx(Gt.div,{initial:{opacity:0,y:40},animate:{opacity:1,y:0},transition:{duration:1,delay:.3},className:"mt-16 relative",children:R.jsx("div",{className:"glass-card p-8 max-w-4xl mx-auto",children:R.jsxs("div",{className:"bg-black/50 rounded-xl p-6 font-mono",children:[R.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[R.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),R.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),R.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),R.jsx("span",{className:"ml-4 text-gray-400",children:"Lemix Terminal"})]}),R.jsxs("div",{className:"space-y-2",children:[R.jsx("div",{className:"text-lime-green",children:'$ lemix "Analyze this website and create a summary report"'}),R.jsx("div",{className:"text-gray-400",children:"🌐 Opening browser..."}),R.jsx("div",{className:"text-gray-400",children:"📊 Extracting data..."}),R.jsx("div",{className:"text-gray-400",children:"📝 Generating report..."}),R.jsx("div",{className:"text-electric-blue",children:"✅ Task completed! Report saved to /reports/website-analysis.pdf"})]})]})})})]})}),R.jsx("section",{className:"section-padding",children:R.jsxs("div",{className:"max-w-7xl mx-auto",children:[R.jsx(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-16",children:R.jsxs("h2",{className:"text-4xl lg:text-6xl font-bold mb-6 text-soft-white",children:["Smarter Than a Chatbot. ",R.jsx("span",{className:"text-electric-blue",children:"Faster Than an Assistant."})]})}),R.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{icon:yT,title:"Web Automation",description:"Interact with websites, extract data, fill forms, and navigate like a pro."},{icon:rT,title:"File & Task Control",description:"Rename files, organize folders, generate PDFs, summarize documents."},{icon:tp,title:"Internet Research",description:"Search the web, compile reports, and deliver cited results in seconds."},{icon:Fy,title:"AI Agent Logic",description:"Lemix plans, reasons, and executes – like a real assistant."},{icon:DT,title:"Custom Workflows",description:"Build or modify task flows – from basic to advanced."},{icon:bT,title:"Real-Time Planning",description:"Breaks down complex goals into executable steps and adapts on the fly."}].map((i,l)=>R.jsxs(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:l*.1},className:"feature-card",children:[R.jsx(i.icon,{className:"w-12 h-12 text-electric-blue mb-4"}),R.jsx("h3",{className:"text-xl font-semibold mb-3 text-soft-white",children:i.title}),R.jsx("p",{className:"text-gray-300",children:i.description})]},l))})]})}),R.jsx("section",{className:"section-padding",children:R.jsxs("div",{className:"max-w-7xl mx-auto",children:[R.jsx(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-16",children:R.jsxs("h2",{className:"text-4xl lg:text-6xl font-bold mb-6 text-soft-white",children:["Replace Your Stack With ",R.jsx("span",{className:"text-lime-green",children:"One Superagent"})]})}),R.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-6",children:[{icon:AT,label:"Browser Tool"},{icon:uT,label:"Python Code Executor"},{icon:fT,label:"File Manager"},{icon:tp,label:"Web Search & Scraper"},{icon:$y,label:"Task Planner"},{icon:iT,label:"Summary & Report Generator"},{icon:Fy,label:"Multi-Model Support"}].map((i,l)=>R.jsxs(Gt.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.5,delay:l*.1},className:"glass-card p-6 text-center hover:bg-white/10 transition-all duration-300",children:[R.jsx(i.icon,{className:"w-8 h-8 text-electric-blue mx-auto mb-3"}),R.jsx("p",{className:"text-sm text-gray-300 font-medium",children:i.label})]},l))}),R.jsx(Gt.p,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.8,delay:.5},className:"text-center text-xl text-gray-300 mt-12",children:"Everything you need, in one window."})]})}),R.jsx("section",{className:"section-padding",children:R.jsxs("div",{className:"max-w-7xl mx-auto",children:[R.jsx(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-16",children:R.jsxs("h2",{className:"text-4xl lg:text-6xl font-bold mb-6 text-soft-white",children:["3 Steps to Let ",R.jsx("span",{className:"text-electric-blue",children:"Lemix Work for You"})]})}),R.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{step:"1️⃣",title:"Login & Connect Your Model",description:"Use your API key (OpenAI, Claude, etc.) to power your agent."},{step:"2️⃣",title:"Start a Chat",description:"Describe your task in natural language."},{step:"3️⃣",title:"Watch It Execute",description:"Lemix plans, acts, and completes it — in real time."}].map((i,l)=>R.jsxs(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:l*.2},className:"text-center",children:[R.jsx("div",{className:"text-6xl mb-6",children:i.step}),R.jsx("h3",{className:"text-2xl font-semibold mb-4 text-soft-white",children:i.title}),R.jsx("p",{className:"text-gray-300 text-lg",children:i.description})]},l))})]})}),R.jsx("section",{className:"section-padding",children:R.jsxs("div",{className:"max-w-7xl mx-auto",children:[R.jsx(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-16",children:R.jsxs("h2",{className:"text-4xl lg:text-6xl font-bold mb-6 text-soft-white",children:["Your Private ",R.jsx("span",{className:"text-lime-green",children:"Command Center"})]})}),R.jsxs(Gt.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.8},className:"glass-card p-8 max-w-6xl mx-auto",children:[R.jsxs("div",{className:"bg-black/50 rounded-xl p-6",children:[R.jsxs("div",{className:"flex items-center justify-between mb-6",children:[R.jsxs("div",{className:"flex items-center gap-2",children:[R.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),R.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),R.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),R.jsx("span",{className:"ml-4 text-gray-400 font-mono",children:"Lemix Chat Interface"})]}),R.jsxs("div",{className:"flex gap-4 text-sm text-gray-400",children:[R.jsx("span",{children:"🧠 GPT-4"}),R.jsx("span",{children:"📁 Files: 3"}),R.jsx("span",{children:"⚙️ Tools: Active"})]})]}),R.jsxs("div",{className:"space-y-4",children:[R.jsxs("div",{className:"flex gap-3",children:[R.jsx("div",{className:"w-8 h-8 bg-electric-blue rounded-full flex items-center justify-center text-white font-bold text-sm",children:"U"}),R.jsx("div",{className:"bg-gray-800 rounded-lg p-3 flex-1",children:R.jsx("p",{className:"text-soft-white",children:"Create a comprehensive market analysis report for the SaaS industry, including competitor pricing and feature comparison"})})]}),R.jsxs("div",{className:"flex gap-3",children:[R.jsx("div",{className:"w-8 h-8 bg-lime-green rounded-full flex items-center justify-center text-black font-bold text-sm",children:"L"}),R.jsxs("div",{className:"bg-gray-700 rounded-lg p-3 flex-1",children:[R.jsx("p",{className:"text-soft-white mb-2",children:"I'll help you create a comprehensive SaaS market analysis. Let me break this down into steps:"}),R.jsxs("div",{className:"space-y-2 text-sm",children:[R.jsxs("div",{className:"flex items-center gap-2 text-lime-green",children:[R.jsx(Wy,{className:"w-4 h-4"}),R.jsx("span",{children:"Research top SaaS companies and their pricing models"})]}),R.jsxs("div",{className:"flex items-center gap-2 text-electric-blue",children:[R.jsx("div",{className:"w-4 h-4 border-2 border-electric-blue rounded animate-spin"}),R.jsx("span",{children:"Analyzing competitor features and positioning"})]}),R.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[R.jsx("div",{className:"w-4 h-4 border-2 border-gray-400 rounded"}),R.jsx("span",{children:"Generate comprehensive report with visualizations"})]})]})]})]})]}),R.jsxs("div",{className:"mt-6 flex items-center gap-4",children:[R.jsxs("div",{className:"flex-1 bg-gray-800 rounded-lg p-3 flex items-center gap-2",children:[R.jsx(Iy,{className:"w-5 h-5 text-gray-400"}),R.jsx("span",{className:"text-gray-400",children:"Type your next task..."})]}),R.jsx("button",{className:"bg-electric-blue hover:bg-blue-600 p-3 rounded-lg transition-colors",children:R.jsx(wr,{className:"w-5 h-5 text-white"})})]})]}),R.jsx("div",{className:"mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[{icon:Iy,label:"Interactive Task Chat"},{icon:gT,label:"Tool History Panel"},{icon:VT,label:"File Upload"},{icon:$y,label:"Plan Breakdown"}].map((i,l)=>R.jsxs("div",{className:"flex flex-col items-center gap-2",children:[R.jsx(i.icon,{className:"w-6 h-6 text-electric-blue"}),R.jsx("span",{className:"text-sm text-gray-300",children:i.label})]},l))})]})]})}),R.jsx("section",{className:"section-padding",children:R.jsxs("div",{className:"max-w-7xl mx-auto",children:[R.jsxs(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-16",children:[R.jsx("h2",{className:"text-4xl lg:text-6xl font-bold mb-6 text-soft-white",children:"Simple Pricing"}),R.jsx("p",{className:"text-xl text-gray-300",children:"Start using Lemix free – No hidden fees."})]}),R.jsx(Gt.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.8},className:"max-w-md mx-auto",children:R.jsxs("div",{className:"glass-card p-8 text-center relative overflow-hidden",children:[R.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-electric-blue/10 to-lime-green/10"}),R.jsxs("div",{className:"relative",children:[R.jsx("h3",{className:"text-2xl font-bold text-soft-white mb-2",children:"Starter"}),R.jsx("div",{className:"text-6xl font-bold text-electric-blue mb-6",children:"$0"}),R.jsx("ul",{className:"space-y-4 text-left mb-8",children:["All features unlocked","Unlimited chat sessions","BYO API key","Access to tools","Use in browser"].map((i,l)=>R.jsxs("li",{className:"flex items-center gap-3",children:[R.jsx(Wy,{className:"w-5 h-5 text-lime-green"}),R.jsx("span",{className:"text-gray-300",children:i})]},l))}),R.jsx("button",{className:"glow-button w-full text-white font-semibold",children:"Get Started Free"}),R.jsx("p",{className:"text-sm text-gray-400 mt-4",children:"🔒 No credit card required to start"})]})]})})]})}),R.jsx("section",{className:"section-padding",children:R.jsxs("div",{className:"max-w-4xl mx-auto",children:[R.jsx(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-16",children:R.jsx("h2",{className:"text-4xl lg:text-5xl font-bold mb-6 text-soft-white",children:"Frequently Asked Questions"})}),R.jsx("div",{className:"space-y-6",children:[{question:"Is Lemix really powerful enough to replace other tools?",answer:"Yes. Lemix uses the latest AI models and structured planning to complete tasks across the web, files, and code."},{question:"Do I need to install anything?",answer:"No. Lemix works entirely in the browser. Just log in and start using it."},{question:"Can I use my own API key?",answer:"Yes. Bring your OpenAI or Claude API keys and start immediately."}].map((i,l)=>R.jsxs(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:l*.1},className:"glass-card p-6",children:[R.jsx("h3",{className:"text-xl font-semibold text-soft-white mb-3",children:i.question}),R.jsx("p",{className:"text-gray-300",children:i.answer})]},l))})]})}),R.jsx("section",{className:"section-padding",children:R.jsx("div",{className:"max-w-4xl mx-auto text-center",children:R.jsxs(Gt.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},children:[R.jsxs("h2",{className:"text-4xl lg:text-6xl font-bold mb-6 text-soft-white",children:["Ready to Experience ",R.jsx("span",{className:"text-electric-blue",children:"True Autonomy?"})]}),R.jsx("p",{className:"text-xl text-gray-300 mb-8",children:"Let Lemix handle the boring work while you focus on what matters."}),R.jsxs(Gt.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"glow-button text-white text-lg px-8 py-4 flex items-center gap-3 mx-auto",children:["Get Started Free — Launch Your First Agent Now",R.jsx(wr,{className:"w-5 h-5"})]})]})})}),R.jsx("footer",{className:"border-t border-white/10 py-12",children:R.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[R.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[R.jsx("div",{className:"mb-6 md:mb-0",children:R.jsx("h3",{className:"text-2xl font-bold text-electric-blue font-mono",children:"Lemix"})}),R.jsxs("div",{className:"flex items-center gap-8 mb-6 md:mb-0",children:[R.jsx("a",{href:"#",className:"text-gray-400 hover:text-soft-white transition-colors",children:"Home"}),R.jsx("a",{href:"#",className:"text-gray-400 hover:text-soft-white transition-colors",children:"Features"}),R.jsx("a",{href:"#",className:"text-gray-400 hover:text-soft-white transition-colors",children:"FAQ"}),R.jsx("a",{href:"#",className:"text-gray-400 hover:text-soft-white transition-colors",children:"Privacy"})]}),R.jsxs("div",{className:"flex items-center gap-4",children:[R.jsx("a",{href:"#",className:"text-gray-400 hover:text-electric-blue transition-colors",children:R.jsx(dT,{className:"w-6 h-6"})}),R.jsx("a",{href:"#",className:"text-gray-400 hover:text-electric-blue transition-colors",children:R.jsx(OT,{className:"w-6 h-6"})})]})]}),R.jsx("div",{className:"mt-8 pt-8 border-t border-white/10 text-center",children:R.jsx("p",{className:"text-gray-400",children:"Copyright © 2025 Lemix. All rights reserved."})})]})})]})}d1.createRoot(document.getElementById("app")).render(R.jsx(l1.StrictMode,{children:R.jsx(CT,{})}));
